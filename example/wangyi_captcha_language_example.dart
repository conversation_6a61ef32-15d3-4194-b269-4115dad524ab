import 'package:flutter/material.dart';
import 'package:wd/core/utils/locale/locale_util.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/shared/widgets/wangyi_captcha/wangyi_captcha.dart';
import 'package:wd/core/constants/constants.dart';

/// Example demonstrating WangYi captcha with language switching
class WangYiCaptchaLanguageExample extends StatefulWidget {
  const WangYiCaptchaLanguageExample({Key? key}) : super(key: key);

  @override
  State<WangYiCaptchaLanguageExample> createState() => _WangYiCaptchaLanguageExampleState();
}

class _WangYiCaptchaLanguageExampleState extends State<WangYiCaptchaLanguageExample> {
  String _result = '';
  
  // Available languages for demonstration
  final List<LocaleEntity> _availableLanguages = [
    const LocaleEntity(name: "简体中文", languageCode: "zh", countryCode: "CN"),
    const LocaleEntity(name: "繁體中文", languageCode: "zh", countryCode: "TW"),
    const LocaleEntity(name: "English", languageCode: "en", countryCode: "US"),
    const LocaleEntity(name: "日本語", languageCode: "ja", countryCode: "JP"),
    const LocaleEntity(name: "한국어", languageCode: "ko", countryCode: "KR"),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WangYi Captcha Language Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Language selection
            const Text(
              'Select Language:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _availableLanguages.map((locale) {
                final isSelected = LocaleUtil().currentLocale.languageCode == locale.languageCode &&
                    LocaleUtil().currentLocale.countryCode == locale.countryCode;
                
                return ChoiceChip(
                  label: Text(locale.name),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      setState(() {
                        LocaleUtil().setCurrentLocale(locale);
                      });
                    }
                  },
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Current language info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Language: ${LocaleUtil().currentLocale.name}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('Locale: ${LocaleUtil().flutterLocale}'),
                  Text('WangYi Language Code: ${_getWangyiLanguage()}'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Captcha button
            ElevatedButton(
              onPressed: _showCaptcha,
              child: const Text('Show Captcha'),
            ),
            
            const SizedBox(height: 16),
            
            // Result display
            if (_result.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  border: Border.all(color: Colors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Captcha Result:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    Text(_result),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Get WangYi language code for current locale
  String _getWangyiLanguage() {
    try {
      final locale = LocaleUtil().flutterLocale;
      final languageCode = locale.languageCode;
      final countryCode = locale.countryCode;
      
      // Map Flutter locale to WangYi captcha supported languages
      if (languageCode == 'zh') {
        if (countryCode == 'TW' || countryCode == 'HK') {
          return 'zh-TW'; // Traditional Chinese
        }
        return 'zh-CN'; // Simplified Chinese (default)
      } else if (languageCode == 'en') {
        return 'en'; // English
      } else if (languageCode == 'ja') {
        return 'ja'; // Japanese
      } else if (languageCode == 'ko') {
        return 'ko'; // Korean
      } else if (languageCode == 'th') {
        return 'th'; // Thai
      } else if (languageCode == 'vi') {
        return 'vi'; // Vietnamese
      } else if (languageCode == 'fr') {
        return 'fr'; // French
      } else if (languageCode == 'ru') {
        return 'ru'; // Russian
      } else if (languageCode == 'ar') {
        return 'ar'; // Arabic
      }
      
      // Default to simplified Chinese if language not supported
      return 'zh-CN';
    } catch (e) {
      // Fallback to simplified Chinese if locale is not available
      return 'zh-CN';
    }
  }

  /// Show captcha with current language settings
  void _showCaptcha() {
    setState(() {
      _result = '';
    });

    WangYiCaptcha().show(
      account: '<EMAIL>',
      captchaId: kWangYiVerityKey,
      onSuccess: (validate) {
        setState(() {
          _result = 'Success! Validate: $validate\nLanguage used: ${_getWangyiLanguage()}';
        });
      },
      onValidateFailClose: () {
        setState(() {
          _result = 'Captcha closed by user';
        });
      },
      onError: () {
        setState(() {
          _result = 'Captcha error occurred';
        });
      },
    );
  }
}
