import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/locale_entity.dart';
import 'package:wd/core/utils/global_config.dart';

const kAppLocale = "kAppLocale";

/// 国际化工具类 / Internationalization utility
class LocaleUtil {
  static final LocaleUtil _instance = LocaleUtil._internal();
  factory LocaleUtil() => _instance;
  LocaleUtil._internal();

  /// 当前语言实体 / Current locale entity
  late LocaleEntity currentLocale;

  /// 支持的语言列表 / Supported locale list
  late List<LocaleEntity> supportLocaleList;

  /// 获取当前 Flutter Locale / Get current Flutter Locale
  Locale get flutterLocale => currentLocale.toLocale();

  /// 获取当前 content-language header / Get current content-language header
  String get contentLanguageTag {
    try {
      return flutterLocale.toLanguageTag().replaceAll("-", "_");
    } catch (e) {
      // 如果 currentLocale 未初始化，返回默认
      return 'en_US';
    }
  }

  /// 初始化语言设置 / Initialize locale settings
  Future<void> init() async {
    supportLocaleList = getSupportLocaleList();
    currentLocale = await setupLocaleEntity();
  }

  /// 设置接口返回的用户语言 / Set remote locale from server
  void setupRemoteLocale(String remoteLocaleTag) {
    final parts = remoteLocaleTag.split("_");
    final remoteLocale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
    final supportLocales = supportLocaleList.map((e) => e.toLocale()).toSet();
    if (supportLocales.contains(remoteLocale)) {
      final entity = supportLocaleList.firstWhere((e) => e.toLocale() == remoteLocale);
      setCurrentLocale(entity);
    }
  }

  /// 设置当前语言 / Set current locale
  void setCurrentLocale(LocaleEntity entity) {
    if (currentLocale == entity) return;
    currentLocale = entity;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = sl<NavigatorService>().navigatorKey.currentState!.context;
      context.setLocale(entity.toLocale());
    });
    saveLocale(entity.toLocale());
    // 通知服务器
    if (sl.isRegistered<UserCubit>()) {
      UserApi.updateLanguage(contentLanguageTag);
    }
  }

  /// 语言选择优先级：
  /// 1. 用户保存的语言设置（如果存在且在支持列表中）
  /// 2. 系统语言（如果在支持列表中）
  /// 3. 服务器配置的默认语言（如果在支持列表中）
  /// 4. 英文（兜底）
  /// / Setup locale entity with priority
  Future<LocaleEntity> setupLocaleEntity() async {
    final supportLocales = supportLocaleList.map((e) => e.toLocale()).toSet();
    // 1. 用户保存
    final savedLocale = await loadSavedLocale();
    if (savedLocale != null && supportLocales.contains(savedLocale)) {
      return supportLocaleList.firstWhere((e) => e.toLocale() == savedLocale);
    }
    // 2. 系统语言
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    if (supportLocales.contains(systemLocale)) {
      return supportLocaleList.firstWhere((e) => e.toLocale() == systemLocale);
    }
    // 3. 服务器默认
    final defaultLocaleTag = GlobalConfig().systemConfig.languageType.defaultLanguage;
    final parts = defaultLocaleTag.split("_");
    final defaultLocale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
    if (supportLocales.contains(defaultLocale)) {
      return supportLocaleList.firstWhere((e) => e.toLocale() == defaultLocale);
    }
    // 4. 兜底
    return supportLocaleList.firstWhere(
      (e) => e.languageCode == 'en' && e.countryCode == 'US',
      orElse: () => supportLocaleList.first,
    );
  }

  /// 获取支持的语言列表 / Get supported locale list
  List<LocaleEntity> getSupportLocaleList() {
    try {
      // 获取API支持的语言列表
      final apiSupportList = GlobalConfig().systemConfig.languageType.list.map((e) {
        final parts = e.dictValue.split("_");
        return Locale(parts[0], parts.length > 1 ? parts[1] : null);
      }).toSet();

      // 直接从kLocaleList中筛选出API支持的语言
      return kLocaleList.where((entity) {
        final locale = entity.toLocale();
        return apiSupportList.contains(locale);
      }).toList();
    } catch (e) {
      // 发生错误时返回默认语言列表
      return kLocaleList;
    }
  }

  /// 加载保存的语言设置 / Load saved locale
  Future<Locale?> loadSavedLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final tag = prefs.getString(kAppLocale);
    if (tag != null) {
      final parts = tag.split('-');
      final locale = Locale(parts[0], parts.length > 1 ? parts[1] : null);
      return locale;
    }
    return null;
  }

  /// 保存语言设置 / Save locale
  Future<void> saveLocale(Locale locale) async {
    await SharedPreferences.getInstance().then((prefs) {
      final languageTag = locale.toLanguageTag();
      prefs.setString(kAppLocale, languageTag);
    });
  }
}
