import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/constants/api_constants.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/system_config_entity.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/constants.dart';

const String _inviteCodeKey = 'inviteCodeFromOpenInstall';
const String _channelCodeKey = 'channelCodeFromOpenInstall';

class GlobalConfig {
  // 私有的构造函数，确保不能直接实例化
  GlobalConfig._internal() {
    loadInviteCode();
  }

  // 是否已检查更新
  bool isCheckAppUpdateDone = false;

  // 启动页是否已加载
  bool isSplashPageLoad = false;

  // 判断是否正在显示引导页
  bool isShowingGuidePage = false;

  // 是否需要进入注册页，用于启动时判断
  bool needToRegister = false;

  // 是否需要进入视频页，用于启动时判断
  bool needRedirectToTiktokPage = false;

  late SystemConfigEntity systemConfig;

  PictureConfig? picConfig;

  // 单例实例
  static final GlobalConfig _instance = GlobalConfig._internal();

  // 提供一个静态方法来获取单例实例
  factory GlobalConfig() => _instance;

  // 用于监听静音状态变化
  final ValueNotifier<bool> isShortVideoMuteNotifier = ValueNotifier<bool>(true);

  // 访问器和修改器
  bool get isShortVideoMute => isShortVideoMuteNotifier.value;

  set isShortVideoMute(bool value) {
    isShortVideoMuteNotifier.value = value;
  }

  static String getAppName() {
    switch (kChannel) {
      case 'WD':
        return 'WD GAME';
      default:
        return 'WD GAME';
    }
  }

  // String appBaseUrl = defaultApiDomain();
  String appBaseUrl = defaultApiDomain();

  static String defaultApiDomain() {
    LogD("当前channel： $kChannel");
    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        // return 'https://flutter.wdtests.com';
        return 'https://wdclus002.com';
      default:
        return kDebug ? 'https://wdclus002.com' : 'https://wdclus002.com';
    }
  }

  static String getEngagelabAppKey() {
    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        return '689a76e7db852a0043f8cf5e';
      default:
        return '689a76e7db852a0043f8cf5e';
    }
  }

  static String getAppDownloadLink() {
    if (kIsWeb) {
      return ApiConstants.baseUrl;
    }

    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        return kDebug ? 'https://wd676-links.vercel.app' : 'https://yl363f.com';
      default:
        return kDebug ? 'https://wd676-links.vercel.app' : 'https://yl363f.com';
    }
  }

  /// 是否展示视频模块
  static bool needShowVideoPage() {
    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        return true;
      default:
        return true;
    }
  }

  /// 是否展示首页奖池
  static bool needShowHomeJackpotWidget() {
    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        return true;
      default:
        return true;
    }
  }

  /// 是否展示USDT提现地址
  static bool needShowManualUSDTWithdrawWidget() {
    switch (AppFlavor.fromChannel()) {
      case AppFlavor.kWD:
        return true;
      default:
        return true;
    }
  }

  /// 设备真实宽度，用于web
  double deviceRealWidth = 0;
  double deviceRealHeight = 0;

  String? _inviteCode;
  String? _channelCode;

  // 获取 inviteCodeFromOpenInstall
  String? get inviteCode => _inviteCode;

  String? get channelCode => _channelCode;

  updateDeviceSize(Size size) {
    if (deviceRealWidth != size.width) {
      deviceRealWidth = size.width;
    }
    if (deviceRealHeight != size.height) {
      deviceRealHeight = size.height;
    }
  }

  // 设置 inviteCodeFromOpenInstall，并保存到本地
  set inviteCode(String? code) {
    _inviteCode = code;
    _saveInviteCodeToLocal(code);
  }

  // 设置 inviteCodeFromOpenInstall，并保存到本地
  set channelCode(String? code) {
    _channelCode = code;
    _saveChannelCodeToLocal(code);
  }

  // 从本地存储读取 inviteCodeFromOpenInstall
  Future<void> loadInviteCode() async {
    final prefs = await SharedPreferences.getInstance();
    _inviteCode = prefs.getString(_inviteCodeKey);
    _channelCode = prefs.getString(_channelCodeKey);
  }

  // 将 inviteCodeFromOpenInstall 保存到本地
  Future<void> _saveInviteCodeToLocal(String? code) async {
    final prefs = await SharedPreferences.getInstance();
    if (code != null) {
      await prefs.setString(_inviteCodeKey, code);
    } else {
      await prefs.remove(_inviteCodeKey);
    }
  }

  // 将 inviteCodeFromOpenInstall 保存到本地
  Future<void> _saveChannelCodeToLocal(String? code) async {
    final prefs = await SharedPreferences.getInstance();
    if (code != null) {
      await prefs.setString(_channelCodeKey, code);
    } else {
      await prefs.remove(_channelCodeKey);
    }
  }

  Future<void> fetchAppConfig() async {
    while (true) {
      final success = await _fetchSystemConfig();
      if (success) break;
      await Future.delayed(const Duration(milliseconds: 300));
    }
  }

  Future<bool> _fetchSystemConfig() async {
    try {
      final res = await UserApi.fetchSystemConfig();
      if (res != null) {
        systemConfig = res;
        return true;
      }
    } catch (e) {
      LogE("请求配置接口异常： $e");
    }
    return false;
  }

  Future<String?> getConfigValueByKey(String key, {bool forceUpdate = false}) async {
    if (forceUpdate) {
      const int maxRetries = 3;
      for (int i = 0; i < maxRetries; i++) {
        final success = await _fetchSystemConfig();
        if (success) break;
        if (i < maxRetries - 1) {
          await Future.delayed(const Duration(milliseconds: 300));
        }
      }
    }

    return systemConfig.getValueByKey(key);
  }

  Future<PictureConfig?> getPictureConfig() async {
    const int maxRetries = 3;
    for (int i = 0; i < maxRetries; i++) {
      final res = await UserApi.fetchPictureConfig();
      if (res != null) {
        picConfig = res;
        break;
      }
      if (i < maxRetries - 1) {
        await Future.delayed(const Duration(milliseconds: 300));
      }
    }

    return picConfig;
  }
}

/// This method determines whether notifications are allowed to be displayed
/// by checking if there's an active suppression timestamp.

Future<bool> checkSuppressionStatus(String key) async {
  final prefs = await SharedPreferences.getInstance();
  final suppressedUntil = prefs.getInt(key);

  return suppressedUntil == null || DateTime.fromMillisecondsSinceEpoch(suppressedUntil).isBefore(DateTime.now());
}
