import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/game_notice_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/game_notice_entity.g.dart';

@JsonSerializable()
class GameNoticeListEntity {
	List<GameNoticeEntity> list = [];

	GameNoticeListEntity();

	factory GameNoticeListEntity.fromJson(Map<String, dynamic> json) => $GameNoticeListEntityFromJson(json);

	Map<String, dynamic> toJson() => $GameNoticeListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
@JsonSerializable()
class GameNoticeEntity {
	String noticeTitle = '';
	int intoHistory = 0;

	GameNoticeEntity();

	factory GameNoticeEntity.fromJson(Map<String, dynamic> json) => $GameNoticeEntityFromJson(json);

	Map<String, dynamic> toJson() => $GameNoticeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}