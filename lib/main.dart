import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_web_frame/flutter_web_frame.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/engage_lab/engage_lab_app.dart';
import 'package:wd/core/utils/connectivity_util.dart';
import 'package:wd/core/utils/file_cache_manager/file_cache_manager.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/core/utils/theme/theme_cubit.dart';
import 'package:wd/core/utils/theme/theme_state.dart';
import 'package:wd/features/page/3_chat/chat_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/shared/widgets/scroll_view/app_scroll_behavior.dart';
import 'package:media_kit/media_kit.dart';
import 'package:tencent_cloud_chat_intl/localizations/tencent_cloud_chat_localizations.dart';

import 'core/theme/themes.dart';
import 'core/utils/app_lifecycle_service/app_lifecycle_service.dart';
import 'core/utils/game_recently_util.dart';
import 'core/utils/locale/locale_util.dart';
import 'core/utils/screenUtil.dart';
import 'features/page/0_tiktok/video_home_cubit.dart';
import 'features/page/0_tiktok/video_library/popular_video/popular_video_cubit.dart';
import 'features/page/0_tiktok/video_library/popular_video/popular_video_filter/popular_video_filter_cubit.dart';
import 'features/page/0_tiktok/video_library/video_library_cubit.dart';
import 'features/page/1_game_home/game_home_cubit.dart';
import 'features/page/2_activity/activity_list_cubit.dart';
import 'features/page/3_transact/topup/topup_cubit.dart';
import 'features/page/3_transact/transact_cubit.dart';
import 'features/page/3_transact/withdraw/withdraw_cubit.dart';
import 'features/page/4_mine/mine_v2_cubit.dart';
import 'features/page/4_mine/notifications/notification_cubit.dart';
import 'features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import 'features/page/main/screens/main_screen_cubit.dart';
import 'features/routers/navigator_utils.dart';
import 'features/routers/route_tracker.dart';
import 'features/routers/routers.dart';
import 'injection_container.dart' as di;

const kDesignSize = Size(440, 1252);

final RouteObserver<ModalRoute<void>> routeObserver = RouteObserver<ModalRoute<void>>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  AppLifecycleService.instance.init();

  ConnectivityUtil().initialize();

  await SystemUtil.init();

  await GlobalConfig().fetchAppConfig();

  await FileCacheManager().init();

  await GameRecentlyUtil().init();

  await LocaleUtil().init();

  if (!kIsWeb) {
    // await SystemUtil.initFirebase();
  }

  MediaKit.ensureInitialized();

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  await EasyLocalization.ensureInitialized();

  SystemUtil.checkAppUpdate();

  Routes.initRoutes();

  await di.init();

  runApp(EasyLocalization(
    supportedLocales: kLocaleList.map((e) => e.toLocale()).toList(),
    path: 'assets/translations',
    startLocale: LocaleUtil().flutterLocale,
    fallbackLocale: const Locale('en', 'US'),
    child: MultiBlocProvider(
        providers: [
          // 管理主题
          BlocProvider<ThemeCubit>(create: (_) => ThemeCubit()),
          // 用户单例
          BlocProvider<UserCubit>(create: (context) => di.sl<UserCubit>()),
          // navBar
          BlocProvider<MainScreenCubit>(create: (_) => di.sl<MainScreenCubit>()),
          BlocProvider<GameHomeCubit>(create: (context) => di.sl<GameHomeCubit>()),
          BlocProvider<ActivityListCubit>(create: (context) => di.sl<ActivityListCubit>()),
          BlocProvider<MineV2Cubit>(create: (context) => di.sl<MineV2Cubit>()),
          BlocProvider<ChatCubit>(create: (context) => di.sl<ChatCubit>()),
          BlocProvider<NotificationsCubit>(create: (context) => di.sl<NotificationsCubit>()),
          BlocProvider<PromotionRewardsCubit>(create: (context) => di.sl<PromotionRewardsCubit>()),
          BlocProvider<PopularVideoCubit>(create: (context) => di.sl<PopularVideoCubit>()),
          BlocProvider<PopularVideoFilterCubit>(create: (context) => di.sl<PopularVideoFilterCubit>()),
          // Other providers...
        ],
        // APP页面
        child: const MyApp()),
    // child: const MyApp()),
  ));
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<StatefulWidget> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  EngageLabUtil engageLabUtil = EngageLabUtil();

  @override
  void initState() {
    super.initState();
    if (SystemUtil.isApp()) {
      // OpenInstallManager.instance.initPlatformState();
      // initEngageLabPlatformState();
    }
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initEngageLabPlatformState() async {
    engageLabUtil.init(appKey: GlobalConfig.getEngagelabAppKey(), channel: "developer-default", debug: kDebug);
  }

  @override
  Widget build(BuildContext context) {
    if (SystemUtil.isWeb()) {
      GlobalConfig().updateDeviceSize(MediaQuery.of(context).size);
    }
    GSScreenUtil().init(
      context: context,
      uiSize: kDesignSize, // 设计稿尺寸
      splitScreenMode: false,
      maxWidth: kIsWeb ? 475 : MediaQuery.of(context).size.width, // 最大宽度
    );

    return PopScope(
      canPop: false,
      child: FlutterWebFrame(
          maximumSize: const Size(475.0, 900),
          enabled: kIsWeb,
          // default is enable, when disable content is full size
          backgroundColor: Colors.black,
          builder: (context) {
            return ScreenUtilInit(
              // 屏幕尺寸
              designSize: kDesignSize,
              // 确保最小文字适配
              minTextAdapt: true,
              builder: (context, child) {
                return MaterialApp(
                    title: GlobalConfig.getAppName(),
                    onGenerateRoute: (settings) {
                      final rawName = settings.name ?? '';
                      final cleanedPath = rawName.replaceAll(RegExp(r'/+$'), '');
                      final uri = Uri.tryParse(cleanedPath);

                      final isHome = uri?.queryParameters['isHome'] ?? '';
                      if (isHome == '3' && GlobalConfig.needShowVideoPage()) {
                        GlobalConfig().needRedirectToTiktokPage = true;
                      }
                      if (uri != null &&
                          uri.queryParameters.containsKey('invite') &&
                          uri.queryParameters['invite'] != null &&
                          !di.sl<UserCubit>().state.isLogin) {
                        // 导航到注册页，并传递参数
                        final inviteCode = uri.queryParameters['invite'];
                        final channelCode = uri.queryParameters['channel'];
                        GlobalConfig().inviteCode = inviteCode;
                        GlobalConfig().channelCode = channelCode;
                        print("inviteCode：${GlobalConfig().inviteCode}");
                        if (GlobalConfig().isSplashPageLoad) {
                          /// 启动页已加载的情况下，直接push到注册页
                          return Routes.router.generator(RouteSettings(name: '/login/register/$inviteCode'));
                        } else {
                          ///  启动页未加载的情况下，将逻辑交给启动页
                          GlobalConfig().needToRegister = true;
                        }
                      }
                      // return Routes.router.generator(const RouteSettings(name: AppRouter.nav));
                      return Routes.router.generator(const RouteSettings(name: AppRouter.launch));
                    },
                    navigatorObservers: [
                      routeObserver,
                      RouteTracker(),
                    ],
                    theme: AppTheme.instance.getThemeDark(),
                    darkTheme: AppTheme.instance.getThemeDark(),
                    locale: context.locale,
                    debugShowCheckedModeBanner: kDebug,
                    scrollBehavior: AppScrollBehavior(),
                    supportedLocales: [
                      ...context.supportedLocales,
                      // 复制原有的 supportedLocales
                      ...TencentCloudChatLocalizations.supportedLocales,
                    ],
                    localizationsDelegates: [
                      ...context.localizationDelegates,
                      // ...TencentCloudChatLocalizations.localizationsDelegates,
                    ],
                    navigatorKey: di.sl<NavigatorService>().navigatorKey,
                    builder: (context, child) {
                      return MediaQuery(
                        /// 设置文字大小不随系统设置改变
                        data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1)),
                        child: EasyLoading.init()(context, child),
                      );
                    });
              },
            );
          }),
    );
  }
}
