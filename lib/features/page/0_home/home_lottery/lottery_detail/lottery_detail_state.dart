import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/lottery_cart_item_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/lottery_result_entity.dart';
import 'package:wd/core/utils/log_util.dart';

class LotteryDetailState extends BaseState {
  late final Lottery? model;
  late final bool isStart; /// 是否已开盘
  late final String currentEvent; /// 当前第xxx期
  late String currentEventDisplay; /// 当前第xxx期 显示的时候把年份去掉
  late final String currentOpenTime; /// 本期开奖时间
  late final String lastEvent; /// 上一期期数
  late String lastEventDisplay; /// 上一期xxx期 显示的时候把年份去掉
  late final String lastResult; /// 上一期结果
  late final String lastResultOpenTime; /// 上一期结果开奖日期
  late final List<LotteryResultRecords> resultList; /// 开奖历史
  late final int countDown; /// 倒计时
  late final int currentTabIndex; /// 侧边栏选中index
  late final int totalCount; /// 已选数量
  late final num totalPrice; /// 已选总价
  late final List<LotteryCartItemEntity> cartList;
  late final double singleOrderAmount;
  late final int gameTabIndex; /// 游戏规则侧边栏选中index
  late final String gameRule; /// 游戏规则
  late final NetState gameRuleNetState; /// 游戏规则网络状态
  LotteryDetailState({
    this.model,
    bool? isStart,
    super.netState,
    super.dataList,
    String? currentEvent,
    String? currentOpenTime,
    String? lastEvent,
    String? lastResult,
    String? lastResultOpenTime,
    List<LotteryResultRecords>? resultList,
    int? countDown,
    int? currentTabIndex,
    int? totalCount,
    num? totalPrice,
    List<LotteryCartItemEntity>? cartList,
    double? singleOrderAmount,
    int? gameTabIndex,
    String? gameRule,
    NetState? gameRuleNetState = NetState.idle,
  }) {
    this.isStart = isStart ?? false;
    this.currentEvent = currentEvent ?? "";
    this.currentOpenTime = currentOpenTime ?? DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    this.lastEvent = lastEvent ?? "";
    this.lastResult = lastResult ?? "";
    this.lastResultOpenTime = lastResultOpenTime ?? "";
    this.resultList = resultList ?? [];
    this.countDown = countDown ?? 0;
    this.currentTabIndex = currentTabIndex ?? 0;
    this.totalCount = totalCount ?? 0;
    this.totalPrice = totalPrice ?? 0;
    this.cartList = cartList ?? [];
    this.singleOrderAmount = singleOrderAmount ?? 10;
    this.gameTabIndex = gameTabIndex ?? 0;
    this.gameRule = gameRule ?? "";
    this.gameRuleNetState = gameRuleNetState ?? NetState.idle;
  }

  LotteryDetailState copyWith({
    Lottery? model,
    bool? isStart,
    NetState? netState,
    NetState? fetchWinningState,
    List? dataList,
    String? currentEvent,
    String? currentOpenTime,
    String? lastEvent,
    String? lastResult,
    String? lastResultOpenTime,
    List<LotteryResultRecords>? resultList,
    int? countDown,
    int? currentTabIndex,
    int? totalCount,
    num? totalPrice,
    List<LotteryCartItemEntity>? cartList,
    double? singleOrderAmount,
    int? gameTabIndex,
    String? gameRule,
    NetState? gameRuleNetState,
  }) {
    LotteryDetailState state = LotteryDetailState(
      model: model ?? this.model,
      isStart: isStart ?? this.isStart,
      netState: netState ?? this.netState,
      dataList: dataList ?? this.dataList,
      currentEvent: currentEvent ?? this.currentEvent,
      currentOpenTime: currentOpenTime ?? this.currentOpenTime,
      lastEvent: lastEvent ?? this.lastEvent,
      lastResult: lastResult ?? this.lastResult,
      lastResultOpenTime: lastResultOpenTime ?? this.lastResultOpenTime,
      resultList: resultList ?? this.resultList,
      countDown: countDown ?? this.countDown,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      totalCount: totalCount ?? this.totalCount,
      totalPrice: totalPrice ?? this.totalPrice,
      cartList: cartList ?? this.cartList,
      singleOrderAmount: singleOrderAmount ?? this.singleOrderAmount,
      gameTabIndex: gameTabIndex ?? this.gameTabIndex,
      gameRule: gameRule ?? this.gameRule,
      gameRuleNetState: gameRuleNetState ?? this.gameRuleNetState,
    );
    try {
      // 开奖时间的年份，用于replace 期数的年份，缩短显示
      state.currentEventDisplay = state.currentEvent;
      state.lastEventDisplay = state.lastEvent;

      if (state.currentEventDisplay.length > 8) {
        String currentEventYear = DateTime.parse(state.currentOpenTime).year.toString();
        state.currentEventDisplay = state.currentEvent.replaceAll(currentEventYear, "");
      }

      if (state.lastEventDisplay.length > 8) {
        String lastEventYear = DateTime.parse(state.lastResultOpenTime).year.toString();
        state.lastEventDisplay = state.lastEvent.replaceAll(lastEventYear, "");
      }

    } catch (e) {
      LogE(">>>>>$e");
    }

    return state;
  }

  List<Object?> get props => [
    model,
    isStart,
    netState,
    currentEvent,
    currentOpenTime,
    lastEvent,
    lastResult,
    lastResultOpenTime,
    resultList,
    dataList,
    totalCount,
    totalPrice,
    cartList,
    singleOrderAmount,
    gameTabIndex,
    gameRule,
    gameRuleNetState,
  ];

}
