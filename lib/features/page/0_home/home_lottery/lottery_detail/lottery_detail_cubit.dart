import 'package:bloc/bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/lottery.dart';
import 'package:wd/core/models/entities/lottery_cart_item_entity.dart';
import 'package:wd/core/models/entities/lottery_current_status_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/models/entities/lottery_odds_entity.dart';
import 'package:wd/core/models/entities/lottery_result_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/lottery_util.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/lottery_confirm_sheet.dart';

import 'lottery_detail_state.dart';

class LotteryDetailCubit extends Cubit<LotteryDetailState> {
  LotteryDetailCubit() : super(LotteryDetailState());

  Map<String, Set<String>> multipleItemsMap = {};

  setLotteryId(Lottery model) {
    emit(state.copyWith(model: model));
    EasyLoading.show();
    reFetchAllData();
  }

  reFetchAllData() async {
    final model = state.model!;

    await fetchCurrentStatus(model);
    fetchHistoryResult(model);
    fetchOddsListData(model);
    EasyLoading.dismiss();
  }

  executeCloseCountDown() {
    if (state.countDown > 0) {
      final countDown = state.countDown - 1;
      emit(state.copyWith(countDown: countDown));
    }
  }

  /// 当前期状态
  fetchCurrentStatus(Lottery model) async {
    LotteryCurrentStatusEntity? status = await LotteryApi.fetchLotteryState(id: model.id);
    if (status != null && !isClosed) {
      int countDown = TimeUtil.calculateTimeDifferenceInSeconds(status.serverDate, status.close);
      emit(state.copyWith(
        isStart: status.start,
        currentEvent: status.order,
        currentOpenTime: status.open,
        countDown: countDown,
      ));
    }
  }

  /// 今日历史开奖结果
  fetchHistoryResult(Lottery model) async {
    LotteryResultEntity? res = await LotteryApi.fetchLotteryTodayResult(id: model.id);
    if (res != null && !isClosed) {
      if (res.records.isNotEmpty) {
        final lastItem = res.records.first;
        emit(state.copyWith(
            lastEvent: lastItem.periodId,
            lastResult: lastItem.openResult,
            lastResultOpenTime: lastItem.openTime,
            resultList: res.records));
      }
    }
  }

  /// 赔率数据
  fetchOddsListData(Lottery model) async {
    final list = await LotteryApi.fetchLotteryOddsList(id: model.id);

    final groupList = LotteryUtil.processLotteryTypeList(
      gameCategoryCode: model.gameCategoryCode,
      oriList: state.dataList?.cast<LotteryOddsGroup>(),
      newList: list,
      openTime: state.currentOpenTime,
      lotteryId: model.id,
    );

    if (isClosed) return;
    if (list.isEmpty) {
      emit(state.copyWith(dataList: groupList, netState: NetState.empty));
    } else {
      emit(state.copyWith(dataList: groupList, netState: NetState.success));
    }
  }

  onChangeCurrentLotteryOddsGroup(LotteryOddsGroup selectModel) {
    final tmp = state.dataList!.map((model) {
      model.isSelected = model == selectModel;
      return model;
    }).toList();
    final tabIndex = state.dataList!.indexOf(selectModel);
    emit(state.copyWith(dataList: tmp, currentTabIndex: tabIndex));
  }

  onSingleSelectOdds({required LotteryOddsSection section, required LotteryOdds item, required bool isSel}) {
    List<LotteryCartItemEntity> tmpList = List.from(state.cartList);

    LotteryCartItemEntity? existingModel;
    try {
      existingModel = tmpList.firstWhere((model) => model.id == item.id);
    } catch (e) {
      existingModel = null;
    }

    // 如果存在，则移除该项
    if (existingModel != null) {
      section.orderCount -= 1;
      tmpList.remove(existingModel);
    } else {
      section.orderCount += 1;
      // 不存在时，添加新项
      tmpList.add(LotteryCartItemEntity()
        ..id = item.id
        ..itemType = item.itemType
        ..betAmount = state.singleOrderAmount
        ..itemObject = item.itemObject);
    }

    emit(state.copyWith(cartList: tmpList));
    updateTotalPrice();
  }

  onMultipleSelectOdds({
    required LotteryOddsSection section,
    required int rule,
    required int lotteryId,
    required LotteryOdds item,
    required bool isSel,
  }) {
    String itemObject = item.itemObject;
    if (itemObject.contains("-")) {
      itemObject = itemObject.split("-").first;
    }

    if (multipleItemsMap.containsKey(item.itemType)) {
      Set<String> itemSet = multipleItemsMap[item.itemType]!;

      if (isSel) {
        itemSet.add(itemObject);
      } else {
        itemSet.remove(itemObject);
        if (itemSet.isEmpty) {
          multipleItemsMap.remove(item.itemType);
        }
      }
    } else {
      multipleItemsMap[item.itemType] = {itemObject};
    }

    Set<String>? itemObjectSet = multipleItemsMap[item.itemType];
    List<LotteryCartItemEntity> tmpList = List.from(state.cartList);
    tmpList.removeWhere((model) => model.itemType == item.itemType);
    if (itemObjectSet != null && itemObjectSet.length >= rule) {
      final cartItem = LotteryCartItemEntity()
        ..option = itemObjectSet.join(",")
        ..rule = rule
        ..id = lotteryId
        ..itemType = item.itemType
        ..betAmount = state.singleOrderAmount
        ..itemObject = itemObject;
      final orderCount = LotteryUtil.calculateCombination(cartItem.option.split(",").length, cartItem.rule);
      section.orderCount = orderCount;
      tmpList.add(cartItem);
    } else {
      section.orderCount = 0;
    }

    emit(state.copyWith(cartList: tmpList));
    updateTotalPrice();
  }

  updateSingleOrderAmount(double amount) {
    final cartList = state.cartList;
    for (LotteryCartItemEntity model in cartList) {
      model.betAmount = amount;
    }
    emit(state.copyWith(singleOrderAmount: amount, cartList: cartList));
    updateTotalPrice();
  }

  updateTotalPrice() {
    int totalCount = 0;
    for (var cartItem in state.cartList) {
      if (cartItem.isLhcGuoGuan) {
        continue;
      }
      if (cartItem.rule == 1) {
        totalCount += 1;
      } else {
        totalCount += LotteryUtil.calculateCombination(cartItem.option.split(",").length, cartItem.rule);
      }
    }
    for (LotteryOddsGroup group in state.dataList!) {
      if (group.name == "过关") {
        totalCount += group.orderCount;
        continue;
      }
      group.orderCount = 0;
      for (LotteryOddsSection section in group.sectionMap.values) {
        group.orderCount += section.orderCount;
      }
    }

    num totalPrice = totalCount * state.singleOrderAmount;
    emit(state.copyWith(totalCount: totalCount, totalPrice: totalPrice));
  }

  /// LHC 过关玩法
  onGuoGuanSelectOdds({
    required LotteryOddsGroup group,
    required LotteryOdds item,
    required bool isSel,
  }) {
    List<LotteryCartItemEntity> tmpList = List.from(state.cartList);
    tmpList.removeWhere((entry) => entry.isLhcGuoGuan == true);

    // 先置0，待重新计算
    group.orderCount = 0;

    Map<String, List<LotteryOdds>> guoGuanSelMap = {};
    for (var entry in group.sectionMap.entries) {
      for (LotteryOdds odds in entry.value.list) {
        if (odds.isSelected) {
          if (guoGuanSelMap.containsKey(entry.key)) {
            guoGuanSelMap[entry.key]!.add(odds);
          } else {
            guoGuanSelMap[entry.key] = [odds];
          }
        }
      }
    }

    if (guoGuanSelMap.entries.length > 1) {
      // List<List<int>> nonEmptyList = guoGuanSelMap.values.map((oddsList) => oddsList.map((odds) => odds.id).toList()).toList();
      List<List<LotteryOdds>> combinations = LhcUtil.cartesianProduct(guoGuanSelMap.values.toList());

      group.orderCount = combinations.length;
      for (List<LotteryOdds> combination in combinations) {
        tmpList.add(LotteryCartItemEntity()
          ..option = combination.map((odds) => odds.id).toList().join(",")
          ..optionName = "[${combination.map((odds) => "${odds.itemType}: ${odds.itemObject}").toList().join(",")}]"
          ..itemType = group.name
          ..id = group.id
          ..betAmount = state.singleOrderAmount
          ..isLhcGuoGuan = true
          ..itemObject = item.itemObject);
      }
    }

    emit(state.copyWith(cartList: tmpList));
    updateTotalPrice();
  }

  onClickConfirmNow(context) {
    if (!state.isStart) {
      return GSEasyLoading.showToast("当前彩票未开盘");
    }

    sl<NavigatorService>().unFocus();
    final currentEvent = state.currentEvent;

    if (sl<UserCubit>().state.balanceInfo == null) return GSEasyLoading.showToast("获取用户余额失败");
    if (state.cartList.isEmpty) return GSEasyLoading.showToast("请选择投注内容");
    if (state.totalPrice == 0) return GSEasyLoading.showToast("请输入/选择投注金额");
    if (state.totalPrice > sl<UserCubit>().state.balanceInfo!.accountMoney) return GSEasyLoading.showToast("余额不足，请充值");
    // final contentStr = state.cartList.map((e) => "${e.itemType}")

    var map = {};
    for (LotteryCartItemEntity model in state.cartList) {
      if (map.containsKey(model.itemType)) {
        List tmp = map[model.itemType];
        if (model.isLhcGuoGuan) {
          tmp.add(model.optionName);
          continue;
        }
        if (model.rule == 1) {
          tmp.add(model.itemObject);
        }
      } else {
        if (model.isLhcGuoGuan) {
          map[model.itemType] = [model.optionName];
          continue;
        }
        if (model.rule == 1) {
          map[model.itemType] = [model.itemObject];
        } else {
          map[model.itemType] = LotteryUtil.generateAllCombinations(model.option.split(","), model.rule);
        }
      }
    }
    final contentStr = map.entries.map((entry) => '[${entry.key}]:${entry.value.join(", ")}').join("\n");

    showLotteryConfirmSheet(context,
        lotteryName: state.model!.lotteryName,
        currentEvent: currentEvent,
        totalAmount: state.totalPrice,
        contentStr: contentStr, onClickConfirm: () {
      if (currentEvent != state.currentEvent) {
        CommonDialog.show(
          context: context,
          title: "期数[${state.currentEvent}]已更新,是否继续下注？",
          complete: () {
            _submit();
          },
        );
        return;
      }
      _submit();
    });
  }

  _submit() async {
    GSEasyLoading.showLoading();
    final flag =
        await LotteryApi.submitBet(period: state.currentEvent, lotteryId: state.model!.id, cartList: state.cartList);
    sl<UserCubit>().fetchUserBalance();
    GSEasyLoading.dismiss();
    if (flag) {
      GSEasyLoading.showToast("投注成功");
      resetData();
    }
  }

  resetData({changeLottery = false}) {
    multipleItemsMap = {};
    if (changeLottery) {
      emit(state.copyWith(
        cartList: [],
        dataList: [],
        currentEvent: "",
        lastEvent: "",
        lastResult: "",
        resultList: [],
        countDown: -1,
      ));
    } else {
      var tmpDataList = List.from(state.dataList!);
      for (LotteryOddsGroup group in tmpDataList) {
        group.orderCount = 0;
        group.sectionMap.values.map((entry) {
          entry.list.map((odds) => odds.isSelected = false).toList();
          entry.selItemCount = 0;
          entry.orderCount = 0;
        }).toList();
      }

      emit(state.copyWith(cartList: [], dataList: tmpDataList));
    }

    updateTotalPrice();
  }

  /// change game tab
  void changeTab(int index) => emit(state.copyWith(gameTabIndex: index));

  /// fetch lottery rules
  void fetchLotteryRule(int lotteryId) async {
    emit(state.copyWith(gameRuleNetState: NetState.loading));
    try {
      final rule = await LotteryApi.fetchLotteryRule(id: lotteryId);
      if (rule != null) {
        emit(state.copyWith(
            gameRule: rule, gameRuleNetState: NetState.success));
      } else {
        emit(state.copyWith(gameRuleNetState: NetState.empty));
      }

    } on Exception catch (_) {
      emit(state.copyWith(gameRuleNetState: NetState.failed));
    }
  }
}
