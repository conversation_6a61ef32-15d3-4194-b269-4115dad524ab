import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_bar/game_list_app_bar.dart';
import 'package:wd/shared/widgets/common_float_screen_widget.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/platform/platform_menu_cell.dart';
import 'package:wd/shared/widgets/text_field/game_search_text_field.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';

import '../../../../core/base/net_error_widget.dart';
import 'game_sub_list_v2_cubit.dart';
import 'game_sub_list_v2_state.dart';

class GameSubListV2Page extends StatefulWidget {
  final GameTypeV2 gameTypeModel;

  const GameSubListV2Page({
    super.key,
    required this.gameTypeModel,
  });

  @override
  State createState() => _GameSubListV2PageState();

}

class _GameSubListV2PageState extends State<GameSubListV2Page> with SingleTickerProviderStateMixin {
  final paddingH = 14.gw;
  final floatWidgetTopPadding = 15.gw;
  final floatWidgetBottomPadding = 20.gw;
  late FloatScreenController floatScreenController;
  TextEditingController searchController = TextEditingController();
  List<GamePlatformV2> gamePlatformV2List = [];

  @override
  void initState() {
    floatScreenController = FloatScreenController(vsync: this);
    // 本地添加全部
    gamePlatformV2List = widget.gameTypeModel.clone().data;
    var allGamePlatformV2 = GamePlatformV2.createAllGame(widget.gameTypeModel.code);
    gamePlatformV2List.insert(0, allGamePlatformV2);
    calculateFloatViewMaxHeight();
    super.initState();
  }

  @override
  void dispose() {
    floatScreenController.dispose();
    searchController.dispose();
    super.dispose();
  }

  void calculateFloatViewMaxHeight() {
    int column = (gamePlatformV2List.length / 4).ceil();
    final axisSpacing = (column - 1) * 8.gw;
    final maxHeight = floatWidgetTopPadding + 134.gw * column + axisSpacing + floatWidgetBottomPadding;
    floatScreenController.updateMaxHeight(maxHeight);
  }

  _buildGridView() {
    final cubit = context.read<GameSubListV2Cubit>();
    return BlocBuilder<GameSubListV2Cubit, GameSubListV2State>(
      builder: (context, state) {
        if (state.dataNetState == NetState.loading) {
          return Center(child: CircularProgressIndicator(color: context.theme.primaryColor,),);
        } else if (state.dataNetState == NetState.failed) {
          return Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: NetErrorWidget(title: 'network_error'.tr(), refreshMethod: () => cubit.fetchData()));
        }

        List<GameV2> list = state.dataList;
        final keyword = state.keyword.toLowerCase();
        if (keyword.isNotEmpty) {
          list = list.where((game) => game.name.toLowerCase().contains(keyword)).toList();
        }

        if (list.isEmpty) {
          return const EmptyWidget();
        }
        return GridView.builder(
          physics: const ScrollPhysics(),
          padding: EdgeInsets.fromLTRB(paddingH, 0, paddingH, 20.gw),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: (GSScreenUtil().screenWidth / 500).floor() + 4, // 每行显示
            childAspectRatio: 86 / 103, // 宽高比
            crossAxisSpacing: 13.gw, // 水平间距
            mainAxisSpacing: 10.gw, // 垂直间距
          ),
          itemCount: list.length,
          itemBuilder: (context, index) {
            final game = list[index];
            return HomeGameCell(
              title: game.name,
              iconUrl: GlobalConfig().systemConfig.gamePicBaseUrl + game.mainImgUrl,
              platIcon: GlobalConfig().systemConfig.gamePicBaseUrl + game.platIcon,
              imageFit: BoxFit.cover,
              isFav: game.isSavour,
              onTap: () => cubit.onClickGameCell(game),
              onTapFav: () => cubit.onClickGameFav(isFav: !game.isSavour, game: game),
            );
          },
        );
      },
    );
  }

  _buildExpandedPlatformListWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(21.gw, floatWidgetTopPadding, 21.gw, 0),
      clipBehavior: Clip.hardEdge,
      width: GSScreenUtil().screenWidth,
      // 宽度占满屏幕
      decoration: BoxDecoration(
        color: context.theme.bottomNavigationBarTheme.backgroundColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.gw),
          bottomRight: Radius.circular(16.gw),
        ),
      ),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        physics: const ClampingScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: (GSScreenUtil().screenWidth / 500).floor() + 4, // 每行显示4个
          childAspectRatio: 94 / 134, // 宽高比
          crossAxisSpacing: 8.gw, // 水平间距
          mainAxisSpacing: 8.gw, // 垂直间距
        ),
        itemCount: gamePlatformV2List.length,
        itemBuilder: (context, index) {
          final platform = gamePlatformV2List[index];
          return PlatformMenuCell(
            title: platform.name,
            iconUrl: GlobalConfig().systemConfig.gamePicBaseUrl + platform.logoUrl,
            isAll: platform.code == GamePlatformV2.allGameCode,
            onTap: () {
              toggleFloatView();
              setState(() {
                context.read<GameSubListV2Cubit>().onChangeSelectedPlatform(platform);
                calculateFloatViewMaxHeight();
              });
            },
          );
        },
      ),
    );
  }

  toggleFloatView() {
    final cubit = BlocProvider.of<GameSubListV2Cubit>(context);
    floatScreenController.toggle();
    cubit.onChangeIsExpanded(!cubit.state.isExpanded);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GameSubListV2Cubit, GameSubListV2State>(
      builder: (context, state) {
        return KeyboardDismisser(
          child: Scaffold(
            appBar: GameListAppBar(
              title: state.platform.name,
              onTapTitle: () => toggleFloatView(),
              isExpanded: state.isExpanded,
            ),
            body: CommonFloatScreenWidget(
              floatScreenController: floatScreenController,
              onStatusChange: (isExpanded) {
                if (isExpanded != state.isExpanded) {
                  BlocProvider.of<GameSubListV2Cubit>(context).onChangeIsExpanded(isExpanded);
                }
              },
              floatChild: _buildExpandedPlatformListWidget(context),
              child: Column(
                children: [
                  SizedBox(height: 14.gw),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: paddingH),
                    child: GameSearchTextField(
                      hintText: 'Pls_enter_game_name'.tr(), // 请输入游戏名称
                      onChanged: (text) {
                        BlocProvider.of<GameSubListV2Cubit>(context).onChangeKeyword(text);
                      },
                      onTapSearch: () {
                        sl<NavigatorService>().unFocus();
                      },
                    ),
                  ),
                  SizedBox(height: 14.gw),
                  Expanded(child: _buildGridView()),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
