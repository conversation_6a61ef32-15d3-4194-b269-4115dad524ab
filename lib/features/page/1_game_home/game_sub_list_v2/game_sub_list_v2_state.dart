import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';

class GameSubListV2State extends Equatable {
  final bool isExpanded;
  final String keyword;
  final GamePlatformV2 platform;
  final NetState dataNetState;
  final List<GameV2> dataList;

  const GameSubListV2State({
    this.isExpanded = false,
    this.keyword = '',
    required this.platform,
    this.dataNetState = NetState.idle,
    this.dataList = const [],
  });

  GameSubListV2State copyWith({
    bool? isExpanded,
    String? keyword,
    GamePlatformV2? platform,
    NetState? dataNetState,
    List<GameV2>? dataList,
  }) {
    return GameSubListV2State(
      isExpanded: isExpanded ?? this.isExpanded,
      keyword: keyword ?? this.keyword,
      platform: platform ?? this.platform,
      dataNetState: dataNetState ?? this.dataNetState,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [
    isExpanded,
    keyword,
    platform,
    dataNetState,
    dataList,
  ];
}
