import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/image_captcha_entity.dart';
import 'package:wd/core/models/country.dart';

enum CaptchaType {
  none, // index = 0 无需验证码
  picture, // index = 1 图片验证码
  wangYi, // index = 2 网易验证码
}

class LoginState extends Equatable {
  final AuthMethodType authMethodType;
  final LoginType loginType;
  final CaptchaType captchaType;
  final String username;
  final String password;
  final bool rememberMe;
  final bool acceptPrivacyPolicy;
  final bool isPasswordVisible;
  final String phone;
  final String smsCode;
  final String? verificationCode;
  ImageCaptchaEntity? captchaModel;
  final Country? selectedCountry;
  final NetState loginStatus;
  final TextEditingController usernameController;
  final TextEditingController phoneController;
  final TextEditingController passwordController;
  final TextEditingController verificationCodeController;
  final TextEditingController smsCodeController;
  final TextEditingController captchaController;

  LoginState({
    this.authMethodType = AuthMethodType.accountOnly,
    this.loginType = LoginType.userName,
    this.captchaType = CaptchaType.wangYi,
    required this.username,
    required this.phone,
    required this.password,
    required this.rememberMe,
    required this.acceptPrivacyPolicy,
    this.smsCode = '',
    this.verificationCode,
    required this.isPasswordVisible,
    required this.loginStatus,
    this.captchaModel,
    this.selectedCountry,
    required this.usernameController,
    required this.phoneController,
    required this.passwordController,
    required this.verificationCodeController,
    required this.smsCodeController,
    required this.captchaController,
  });

  LoginState copyWith({
    AuthMethodType? authMethodType,
    LoginType? loginType,
    CaptchaType? captchaType,
    String? username,
    String? phone,
    String? smsCode,
    String? password,
    bool? rememberMe,
    bool? acceptPrivacyPolicy,
    bool? isPasswordVisible,
    String? verificationCode,
    bool? isShowCaptcha,
    ImageCaptchaEntity? captchaModel,
    Country? selectedCountry,
    NetState? loginStatus,
    TextEditingController? usernameController,
    TextEditingController? phoneController,
    TextEditingController? passwordController,
    TextEditingController? smsCodeController,
    TextEditingController? verificationCodeController,
    TextEditingController? captchaController,
  }) {
    return LoginState(
      authMethodType: authMethodType ?? this.authMethodType,
      loginType: loginType ?? this.loginType,
      captchaType: captchaType ?? this.captchaType,
      username: username ?? this.username,
      phone: phone ?? this.phone,
      smsCode: smsCode ?? this.smsCode,
      password: password ?? this.password,
      rememberMe: rememberMe ?? this.rememberMe,
      acceptPrivacyPolicy: acceptPrivacyPolicy ?? this.acceptPrivacyPolicy,
      isPasswordVisible: isPasswordVisible ?? this.isPasswordVisible,
      verificationCode: verificationCode ?? this.verificationCode,
      captchaModel: captchaModel ?? this.captchaModel,
      selectedCountry: selectedCountry ?? this.selectedCountry,
      loginStatus: loginStatus ?? this.loginStatus,
      usernameController: usernameController ?? this.usernameController,
      phoneController: phoneController ?? this.phoneController,
      passwordController: passwordController ?? this.passwordController,
      verificationCodeController: verificationCodeController ?? this.verificationCodeController,
      smsCodeController: smsCodeController ?? this.smsCodeController,
      captchaController: captchaController ?? this.captchaController,
    );
  }

  @override
  List<Object?> get props => [
        authMethodType,
        loginType,
        captchaType,
        username,
        phone,
        smsCode,
        password,
        rememberMe,
        acceptPrivacyPolicy,
        verificationCode,
        isPasswordVisible,
        captchaModel,
        selectedCountry,
        loginStatus,
        verificationCodeController,
        phoneController,
        smsCodeController,
        captchaController,
      ];
}
