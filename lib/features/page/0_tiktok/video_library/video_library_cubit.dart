import 'package:bloc/bloc.dart';
import 'package:fluro/fluro.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/models/entities/video_filter_entity.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/shared/widgets/tiktok/home_video_library_filter.dart';

import 'video_library_state.dart';

class VideoLibraryCubit extends Cubit<VideoLibraryState> {
  bool _isLoading = false;
  int retryCount = 0;

  VideoLibraryCubit() : super(VideoLibraryState().init()) {
    fetchData();
  }

  fetchData({bool forcedUpdate = false}) {
    if (forcedUpdate || state.filterCategories.isEmpty) {
      fetchCategories();
    }
    if (forcedUpdate || state.videoList.isEmpty) {
      fetchVideoDataList();
    }
  }

  void updatePageNo(int pageNo) {
    emit(state.copyWith(
      pageNo: pageNo,
      videoList: pageNo == 1 ? [] : state.videoList,
    ));
  }

  void updatePageNoToNext() {
    if (!_isLoading && !state.isNoMoreDataState!) {
      emit(state.copyWith(pageNo: state.pageNo + 1));
      fetchVideoDataList();
    }
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    emit(state.copyWith(isNoMoreDataState: isNoMoreDataState));
  }

  Future<void> fetchCategories() async {
    List<VideoFilterEntity> categories = await VideoApi.fetchHomeVideoFilterList();

    emit(state.copyWith(
      filterCategories: categories.map((e) => HomeVideoLibraryFilterItem.fromVideoFilterEntity(e)).toList(),
      netState: categories.isEmpty ? NetState.showReload : NetState.success,
    ));
    fetchVideoDataList();
  }

  void selectFilter(HomeVideoLibraryFilterItem selectedItem) {
    emit(state.copyWith(
      currentVideoCategory: selectedItem,
      pageNo: 1,
      videoList: [], // Clear existing videos when filter changes
    ));
    fetchVideoDataList();
  }

  Future<void> fetchVideoDataList({String? keyword}) async {
    if (_isLoading) return;
    _isLoading = true;

    try {
      final result = await VideoApi.fetchHomeVideoList(
        pageNo: state.pageNo,
        videoCategory: state.currentVideoCategory.title == 'all'.tr() ? '' : state.currentVideoCategory.id,
        keyword: keyword,
      );

      // 创建新的视频列表
      final List<VideoListRecords> newVideoList;
      if (state.pageNo == 1) {
        newVideoList = result.records;
      } else {
        final uniqueVideos = {...state.videoList, ...result.records};
        newVideoList = uniqueVideos.toList();
      }

      emit(state.copyWith(
        videoList: newVideoList,
        isNoMoreDataState: result.total <= newVideoList.length,
        listNetState: newVideoList.isEmpty ? NetState.empty : NetState.success,
      ));
    } finally {
      _isLoading = false;
    }
  }

  goToSearchPage() {
    sl<NavigatorService>().push(AppRouter.videoSearch, transition: TransitionType.none);
  }
}
