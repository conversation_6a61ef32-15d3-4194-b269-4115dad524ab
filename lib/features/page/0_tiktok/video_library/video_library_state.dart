import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/shared/widgets/tiktok/home_video_library_filter.dart';

class VideoLibraryState {
  final TextEditingController searchController;
  final NetState netState;
  final NetState listNetState;
  final List<VideoListRecords> videoList;
  final List<HomeVideoLibraryFilterItem> filterCategories;
  final HomeVideoLibraryFilterItem currentVideoCategory;
  final int pageNo;
  final bool? isNoMoreDataState;

  /// 是否还有更多数据

  VideoLibraryState({
    TextEditingController? searchController,
    this.netState = NetState.loading,
    this.listNetState = NetState.loading,
    this.videoList = const [],
    this.filterCategories = const [],
    HomeVideoLibraryFilterItem? currentVideoCategory,
    this.pageNo = 1,
    this.isNoMoreDataState = false,
  })  : searchController = searchController ?? TextEditingController(),
                 currentVideoCategory =
             currentVideoCategory ?? HomeVideoLibraryFilterItem(id: 'videoCategoryAll', title: 'all'.tr());

  /// 初始化状态
  VideoLibraryState init() {
    return this;
  }

  VideoLibraryState copyWith({
    TextEditingController? searchController,
    NetState? netState,
    NetState? listNetState,
    List<VideoListRecords>? videoList,
    List<HomeVideoLibraryFilterItem>? filterCategories,
    HomeVideoLibraryFilterItem? currentVideoCategory,
    int? pageNo,
    bool? isNoMoreDataState,
  }) {
    return VideoLibraryState(
      searchController: searchController ?? this.searchController,
      netState: netState ?? this.netState,
      listNetState: listNetState ?? this.listNetState,
      videoList: videoList ?? this.videoList,
      filterCategories: filterCategories ?? this.filterCategories,
      currentVideoCategory: currentVideoCategory ?? this.currentVideoCategory,
      pageNo: pageNo ?? this.pageNo,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
    );
  }
}
