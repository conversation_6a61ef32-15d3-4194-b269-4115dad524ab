part of 'popular_video_cubit.dart';

class PopularVideoState extends BaseState with EquatableMixin {
  /// 顶部筛选数据
  final List<HomeVideoLibraryFilterItem> filterCategories;

  /// 选中的数据
  final HomeVideoLibraryFilterItem currentVideoCategory;

  final VideoHotTagEntity? videoHotTags;

  /// 热门列表
  final VideoHotMoviesEntity? moviesListMap;

  /// 点击筛选后的列表
  final List<VideoHotMovies> filterVideoList;

  /// 搜索框关键词
  final String searchKeyword;

  PopularVideoState({
    this.videoHotTags,
    this.moviesListMap,
    this.filterCategories = const [],
    this.filterVideoList = const [],
    this.searchKeyword = "",
    HomeVideoLibraryFilterItem? currentVideoCategory,

    // 基类字段
    super.netState,
    super.isNoMoreDataState,
    super.isNetWorkFinish,
    super.dataList,
    super.netLoadCount,
  }) : currentVideoCategory = currentVideoCategory ?? HomeVideoLibraryFilterItem(id: 'all', title: 'all'.tr());

  PopularVideoState copyWith({
    VideoHotTagEntity? videoHotTags,
    VideoHotMoviesEntity? moviesListMap,
    List<HomeVideoLibraryFilterItem>? filterCategories,
    List<VideoHotMovies>? filterVideoList,
    HomeVideoLibraryFilterItem? currentVideoCategory,
    NetState? netState,
    String? searchKeyword,
  }) {
    return PopularVideoState(
      videoHotTags: videoHotTags ?? this.videoHotTags,
      moviesListMap: moviesListMap ?? this.moviesListMap,
      filterCategories: filterCategories ?? this.filterCategories,
      filterVideoList: filterVideoList ?? this.filterVideoList,
      currentVideoCategory: currentVideoCategory ?? this.currentVideoCategory,
      netState: netState ?? this.netState,
      searchKeyword: searchKeyword ?? this.searchKeyword,
    );
  }

  @override
  List<Object?> get props => [
        videoHotTags,
        moviesListMap,
        filterCategories,
        filterVideoList,
        currentVideoCategory,
        searchKeyword,
      ];
}
