import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';

import '../../../../../../core/models/entities/video_hot_movies_entity.dart';

class PopularVideoSearchState extends Equatable {
  final List<VideoHotMovies> videoList;
  final List<String> searchHistory;
  final List<String> hotSearches;
  final String searchText;
  final bool isSearching;
  final bool isNoMoreDataState;
  final NetState netState;
  final int pageNo;
  final int pageSize;

  const PopularVideoSearchState({
    this.videoList = const [],
    this.searchHistory = const [],
    this.hotSearches = const [],
    this.searchText = '',
    this.isSearching = false,
    this.isNoMoreDataState = false,
    this.netState = NetState.idle,
    this.pageNo = 1,
    this.pageSize = 10,
  });

  PopularVideoSearchState copyWith({
    List<VideoHotMovies>? videoList,
    List<String>? searchHistory,
    List<String>? hotSearches,
    String? searchText,
    bool? isSearching,
    bool? isNoMoreDataState,
    NetState? netState,
    int? pageNo,
    int? pageSize,
  }) {
    return PopularVideoSearchState(
      videoList: videoList ?? this.videoList,
      searchHistory: searchHistory ?? this.searchHistory,
      hotSearches: hotSearches ?? this.hotSearches,
      searchText: searchText ?? this.searchText,
      isSearching: isSearching ?? this.isSearching,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      netState: netState ?? this.netState,
      pageNo: pageNo ?? this.pageNo,
      pageSize: pageSize ?? this.pageSize,
    );
  }

  @override
  List<Object?> get props => [
        videoList,
        searchHistory,
        hotSearches,
        searchText,
        isSearching,
        isNoMoreDataState,
        netState,
        pageNo,
        pageSize,
      ];
}
