import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../../core/models/apis/video.dart';
import '../../../../../../shared/widgets/easy_loading.dart';
import 'popular_video_search_state.dart';

class PopularVideoSearchCubit extends Cubit<PopularVideoSearchState> {
  static const String _searchHistoryKey = 'popular_video_search_history';
  static const int _maxHistoryItems = 10;

  PopularVideoSearchCubit() : super(const PopularVideoSearchState()) {
    _loadSearchHistory();
  }

  Future<void> _loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList(_searchHistoryKey) ?? [];
    emit(state.copyWith(searchHistory: history));
  }

  Future<void> _saveSearchHistory(List<String> history) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_searchHistoryKey, history);
  }

  void updateSearch(String text) {
    emit(state.copyWith(searchText: text));
  }

  void startSearch(String query) {
    if (query.trim().isEmpty) return;

    // Add to search history
    final history = List<String>.from(state.searchHistory);
    // Remove if already exists
    history.removeWhere((item) => item == query);
    // Add to the beginning
    history.insert(0, query);
    // Limit history size
    if (history.length > _maxHistoryItems) {
      history.removeLast();
    }

    _saveSearchHistory(history);

    emit(state.copyWith(
      searchText: query,
      searchHistory: history,
      isSearching: true,
      videoList: [],
      netState: NetState.loading,
      pageNo: 1,
      isNoMoreDataState: false,
    ));

    fetchVideoDataList();
  }

  void cancelSearch() {
    emit(state.copyWith(
      isSearching: false,
      videoList: [],
    ));
  }

  void clearHistory() {
    _saveSearchHistory([]);
    emit(state.copyWith(searchHistory: []));
  }

  void updatePageNo(int pageNo) {
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void updateIsNoMoreDataState(bool isNoMoreData) {
    emit(state.copyWith(isNoMoreDataState: isNoMoreData));
  }

  Future<void> fetchVideoDataList() async {
    if (state.pageNo == 1) {
      GSEasyLoading.showLoading();
    }

    try {
      final result = await VideoApi.fetchHotVideoList(
        pageNo: state.pageNo,
        videoTitle: state.searchText,
      );

      final List<VideoHotMovies> newList;
      if (state.pageNo == 1) {
        newList = result.records ?? [];
      } else {
        final uniqueVideos = {...state.videoList, ...result.records ?? []};
        newList = uniqueVideos.toList();
      }

      final total = result.total ?? 0;
      final currentCount = newList.length;
      final hasMoreData = total > currentCount;
      final isEmpty = newList.isEmpty ? NetState.empty : NetState.success;

      emit(state.copyWith(
        videoList: newList,
        netState: isEmpty,
        isNoMoreDataState: !hasMoreData,
      ));

      GSEasyLoading.dismiss();
    } catch (e) {
      debugPrint('Error fetching videos: $e');
      emit(state.copyWith(
        netState: state.videoList.isEmpty ? NetState.empty : state.netState,
      ));
      GSEasyLoading.dismiss();
    }
  }

}
