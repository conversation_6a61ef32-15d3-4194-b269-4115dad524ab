import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:wd/core/models/entities/video_hot_tag_entity.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/tiktok/home_video_library_filter.dart';

part 'popular_video_state.dart';

class PopularVideoCubit extends Cubit<PopularVideoState> {
  PopularVideoCubit() : super(PopularVideoState()) {
    fetchData();
  }

  bool get isPopular => state.currentVideoCategory.id == "all";

  void fetchData() {

    fetchVideoHotTags();
    isPopular ? fetchHotVideo() : fetchHotVideoList();
  }

  void reset() {
    emit(PopularVideoState());
    fetchData();
  }

  Future<void> fetchVideoHotTags() async {
    final videoHotTags = await VideoApi.fetchVideoHotTags();
    if (videoHotTags != null) {
      final filterList = [
        HomeVideoLibraryFilterItem(id: "all", title: "all".tr()),
        ...?videoHotTags.moviesCategory?.map(HomeVideoLibraryFilterItem.fromVideoHotTagMoviesCategory)
      ];
      emit(state.copyWith(filterCategories: filterList, currentVideoCategory: filterList.first));
    }
  }

  Future<void> fetchHotVideo() async {
    if (state.netState == NetState.loading) return;
    emit(state.copyWith(netState: NetState.loading));

    try {
      final result = await VideoApi.fetchHotVideo(pageNo: 1);

      final VideoHotMoviesEntity newVideoList = result;

      emit(state.copyWith(
        moviesListMap: newVideoList,
        netState: NetState.success,
      ));
    } catch (e) {
      emit(state.copyWith(
        netState: state.moviesListMap == null ? NetState.showReload : NetState.failed,
      ));
    }
  }

  Future<void> fetchHotVideoList() async {
    if (state.netState == NetState.loading) return;
    emit(state.copyWith(netState: NetState.loading));
    GSEasyLoading.showLoading();
    try {
      final result = await VideoApi.fetchHotVideoList(
        pageNo: 1,
        videoCategory: isPopular ? null :state.currentVideoCategory.id,
        isHotMovies: '2',
      );

      emit(state.copyWith(
        filterVideoList: result.records,
        netState: result.records.isEmpty ? NetState.empty : NetState.success,
      ));
      GSEasyLoading.dismiss();
    } catch (e) {
      emit(state.copyWith(
        netState: NetState.failed,
      ));
      GSEasyLoading.dismiss();
    }
  }

  void onSelectCategoryFilter(HomeVideoLibraryFilterItem item) {
    emit(state.copyWith(
      currentVideoCategory: item,
    ));
    if (isPopular) {
      fetchHotVideo();
    } else {
      fetchHotVideoList();
    }
  }
}
