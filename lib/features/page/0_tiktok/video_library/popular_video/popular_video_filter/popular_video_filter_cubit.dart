import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/models/apis/video.dart';
import '../../../../../../core/models/entities/video_hot_movies_entity.dart';
import '../../../../../../core/models/entities/video_hot_tag_entity.dart';

part 'popular_video_filter_state.dart';

class PopularVideoFilterCubit extends Cubit<PopularVideoFilterState> {
  PopularVideoFilterCubit() : super(const PopularVideoFilterState()) {
    fetchVideoHotTags();
    fetchHotVideo();
  }

  Future<void> fetchVideoHotTags() async {
    final videoHotTags = await VideoApi.fetchVideoHotTags();
    if (videoHotTags != null) {
      emit(state.copyWith(videoHotTags: videoHotTags));
    } else {
      emit(state.copyWith(videoHotTags: null));
    }
  }

  void updatePageNoToNext() {
    if (!state.isLoading && !state.isNoMoreDataState) {
      emit(state.copyWith(pageNo: state.pageNo + 1));
      fetchHotVideoList();
    }
  }

  Future<void> fetchHotVideo() async {
    if (state.isLoading) return;
    emit(state.copyWith(isLoading: true));

    try {
      final result = await VideoApi.fetchHotVideo(pageNo: state.pageNo);

      final VideoHotMoviesEntity newVideoList = result;

      emit(state.copyWith(
        hotVideoList: newVideoList,
        isLoading: false,
        isError: false,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isError: true,
      ));
    }
  }

  Future<void> fetchHotVideoList() async {
    if (state.isLoading || state.isNoMoreDataState) return;
    emit(state.copyWith(isLoading: true));
    if (state.pageNo == 1) {
      GSEasyLoading.showLoading();
    }
    try {
      final result = await VideoApi.fetchHotVideoList(
        pageNo: state.pageNo,
        videoCategory: state.categoryFilter == '热门' ? null : state.categoryFilter,
        videoYear: state.yearFilter == '热门' ? null : state.yearFilter,
        videoCountry: state.areaFilter == '热门' ? null : state.areaFilter,
        isHotMovies: state.videoType?.dictKey,
        videoTitle: state.videoTitle,
      );

      final List<VideoHotMovies> filterVideoList;
      if (state.pageNo == 1) {
        filterVideoList = result.records ?? [];
      } else {
        final uniqueVideos = {...state.filterVideoList ?? [], ...result.records ?? []};
        filterVideoList = uniqueVideos.toList();
      }

      final total = result.total ?? 0;
      final currentCount = filterVideoList.length;
      final hasMoreData = total > currentCount;
      final isEmpty = filterVideoList.isEmpty ? NetState.empty : NetState.success;

      emit(state.copyWith(
          filterVideoList: filterVideoList,
          isLoading: false,
          isError: false,
          popularVideoNetState: isEmpty,
          isNoMoreDataState: !hasMoreData));
      GSEasyLoading.dismiss();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isError: true,
      ));
      GSEasyLoading.dismiss();
    }
  }

  void updateVideoTitle(String value) {
    emit(state.copyWith(videoTitle: value));
    resetFilter();
  }

  void updateVideoTypeFilter(VideoHotTagMoviesCategory item) {
    emit(state.copyWith(videoType: item));
    resetFilter();
    fetchHotVideoList();
  }

  void updateAreaFilter(String item) {
    emit(state.copyWith(areaFilter: item));
    resetFilter();
    fetchHotVideoList();
  }

  void updateYearFilter(String item) {
    emit(state.copyWith(yearFilter: item));
    resetFilter();
    fetchHotVideoList();
  }

  void updateCategoryFilter(String item) {
    emit(state.copyWith(categoryFilter: item));
    resetFilter();
    fetchHotVideoList();
  }

  void onSelectCategoryFilter({String? item}) {
    emit(state.copyWith(
      videoType: VideoHotTagMoviesCategory()
        ..dictKey = '2'
        ..dictValue = '热播榜',
      areaFilter: '热门',
      yearFilter: '热门',
      categoryFilter: item ?? state.categoryFilter,
    ));
    resetFilter();
    fetchHotVideoList();
  }

  void resetFilter() {
    emit(state.copyWith(
      isNoMoreDataState: false,
      pageNo: 1,
      filterVideoList: [],
      isLoading: false,
    ));
  }
}
