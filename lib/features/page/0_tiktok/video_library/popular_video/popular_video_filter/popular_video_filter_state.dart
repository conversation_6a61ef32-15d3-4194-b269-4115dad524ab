part of 'popular_video_filter_cubit.dart';

class PopularVideoFilterState extends Equatable {
  final VideoHotTagEntity? videoHotTags;
  final int pageNo;
  final bool isNoMoreDataState;
  final VideoHotMoviesEntity? hotVideoList;
  final List<VideoHotMovies>? filterVideoList;
  final NetState hotVideosVideoNetState;
  final bool isLoading;
  final bool isError;
  final String areaFilter;
  final String yearFilter;
  final String categoryFilter;
  final String videoTitle;
  final VideoHotTagMoviesCategory? videoType;

  const PopularVideoFilterState({
    this.videoHotTags,
    this.pageNo = 1,
    this.isNoMoreDataState = false,
    this.hotVideoList,
    this.filterVideoList,
    this.hotVideosVideoNetState = NetState.idle,
    this.isLoading = false,
    this.isError = false,
    this.areaFilter = "",
    this.yearFilter = "",
    this.categoryFilter = '热门',
    this.videoType,
    this.videoTitle = "",
  });

  PopularVideoFilterState copyWith(
      {VideoHotTagEntity? videoHotTags,
      int? pageNo,
      bool? isNoMoreDataState,
      VideoHotMoviesEntity? hotVideoList,
      List<VideoHotMovies>? filterVideoList,
      NetState? popularVideoNetState,
      bool? isLoading,
      bool? isError,
      String? areaFilter,
      String? yearFilter,
      String? categoryFilter,
      String? videoTitle,
      VideoHotTagMoviesCategory? videoType}) {
    return PopularVideoFilterState(
        videoHotTags: videoHotTags ?? this.videoHotTags,
        pageNo: pageNo ?? this.pageNo,
        isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
        hotVideoList: hotVideoList ?? this.hotVideoList,
        hotVideosVideoNetState: popularVideoNetState ?? hotVideosVideoNetState,
        isLoading: isLoading ?? this.isLoading,
        isError: isError ?? this.isError,
        areaFilter: areaFilter ?? this.areaFilter,
        yearFilter: yearFilter ?? this.yearFilter,
        categoryFilter: categoryFilter ?? this.categoryFilter,
        filterVideoList: filterVideoList ?? this.filterVideoList,
        videoType: videoType ?? this.videoType,
        videoTitle: videoTitle ?? this.videoTitle);
  }

  @override
  List<Object?> get props => [
        videoHotTags,
        pageNo,
        isNoMoreDataState,
        hotVideoList,
        hotVideosVideoNetState,
        isLoading,
        isError,
        areaFilter,
        yearFilter,
        categoryFilter,
        videoType,
        filterVideoList,
        videoTitle,
      ];
}
