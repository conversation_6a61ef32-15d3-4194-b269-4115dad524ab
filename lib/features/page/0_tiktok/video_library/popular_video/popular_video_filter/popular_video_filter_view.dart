import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/base/base_stateful_page.dart';
import '../../../../../../core/base/common_refresher.dart';
import '../../../../../../core/base/empty_widget.dart';
import '../../../../../../core/base/net_error_widget.dart';
import '../../../../../../core/models/entities/video_hot_tag_entity.dart';
import '../../../../../../core/utils/screenUtil.dart';
import '../../../../../../injection_container.dart';
import 'popular_video_filter.dart';
import '../../../../../../shared/widgets/tiktok/video_silver_movie_grid_view.dart';
import '../../video_library_cubit.dart';
import 'popular_video_filter_cubit.dart';

class PopularVideoFilterView extends BasePage {
  final int animationCount;
  final String category;

  const PopularVideoFilterView({super.key, required this.animationCount, required this.category});

  @override
  BasePageState<BasePage> getState() {
    return _PopularVideoFilterState();
  }
}

class _PopularVideoFilterState extends BasePageState<PopularVideoFilterView> with RouteAware {
  final videoType = [
    VideoHotTagMoviesCategory()
      ..dictKey = '1'
      ..dictValue = '新上线',
    VideoHotTagMoviesCategory()
      ..dictKey = '2'
      ..dictValue = '热播榜',
  ];
  final paddingH = 14.gw;
  late final topMargin = MediaQuery.of(context).padding.top + 20 + 12.gw + 26.gw;

  final RefreshController refreshController = RefreshController(initialRefresh: false);

  void _onLoading() {
    context.read<PopularVideoFilterCubit>().updatePageNoToNext();
  }

  void _listener(BuildContext context, PopularVideoFilterState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  String _getCategoryTitle(String? category) {
    if (category == null) return '';
    VideoHotTagEntity? videoHotTags = sl<PopularVideoFilterCubit>().state.videoHotTags;
    if (videoHotTags == null) return '';
    final tag = videoHotTags.moviesCategory
        ?.firstWhere((element) => element.dictKey == category, orElse: () => VideoHotTagMoviesCategory());
    return tag?.dictValue ?? '';
  }

  @override
  void initState() {
    super.initState();
    pageTitle = '全部电影';
  }

  @override
  Widget right() {
    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: () {
        sl<NavigatorService>().push(AppRouter.popularVideoSearch);
      },
      icon: Image.asset(
        "assets/images/tiktok/icon_textField_search.png",
        width: 20.gw,
        height: 20.gw,
        color: const Color(0xff6A7391),
        fit: BoxFit.contain,
      ),
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = context.read<VideoLibraryCubit>();

    // return Container();
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) => context.read<PopularVideoFilterCubit>().onSelectCategoryFilter(),
      child: BlocConsumer<PopularVideoFilterCubit, PopularVideoFilterState>(
          listener: _listener,
          builder: (context, state) {
            if (state.isError) {
              return Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: NetErrorWidget(title: 'network_error'.tr(), refreshMethod: () => cubit.fetchData()));
            }
            bool isEmpty = state.hotVideosVideoNetState == NetState.empty;
            return Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: paddingH),
                child: CommonRefresher(
                  refreshController: refreshController,
                  bgColor: Theme.of(context).scaffoldBackgroundColor,
                  enablePullDown: false,
                  enablePullUp: !isEmpty,
                  onRefresh: null,
                  onLoading: _onLoading,
                  listWidget: CustomScrollView(
                    slivers: [
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: _FilterHeaderDelegate(
                          animationCount: widget.animationCount,
                          videoType: videoType,
                        ),
                      ),
                      if (isEmpty) ...[
                        SliverToBoxAdapter(
                            child: SizedBox(
                          height: 400.gw,
                          child: const EmptyWidget(),
                        )),
                      ] else ...[
                        // 视频列表
                        SliverPadding(
                          padding: EdgeInsets.only(top: 15.gw),
                          sliver: VideoSliverMovieGridView(
                            filterVideoList: state.filterVideoList,
                            isGrouped: false,
                            hideCategory: true,
                            onTapCell: (videoEntity) {
                              final model = VideoListRecords()
                                ..id = videoEntity.id ?? 0
                                ..videoImage = videoEntity.videoImage ?? ''
                                ..videoTitle = videoEntity.videoTitle ?? ''
                                ..videoTime = videoEntity.videoTime ?? ''
                                ..videoYear = videoEntity.videoYear ?? ''
                                ..videoCategory = videoEntity.videoCategory ?? ''
                                ..videoType = videoEntity.videoType ?? 0
                                ..videoTags = videoEntity.videoTags ?? ''
                                ..videoCountry = videoEntity.videoCountry ?? ''
                                ..videoClarity = videoEntity.videoClarity ?? ''
                                ..videoBottomTag = videoEntity.videoBottomTag ?? ''
                                ..playCount = videoEntity.playCount ?? 0
                                ..hide = videoEntity.hide ?? 0
                                ..createTime = videoEntity.createTime ?? '';
                              AuthUtil.checkIfLogin(() {
                                sl<NavigatorService>().push(
                                  AppRouter.videoDetail,
                                  arguments: {
                                    "model": model,
                                    "videoCategory": videoEntity.videoCategory,
                                    "pageTitle": '热门电影',
                                  },
                                );
                              });
                            },
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}

class _FilterHeaderDelegate extends SliverPersistentHeaderDelegate {
  final int animationCount;
  final List<VideoHotTagMoviesCategory> videoType;

  _FilterHeaderDelegate({
    required this.animationCount,
    required this.videoType,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: AnimationLimiter(
        key: ValueKey(animationCount),
        child: BlocBuilder<PopularVideoFilterCubit, PopularVideoFilterState>(
          builder: (context, state) {
            final tags = state.videoHotTags;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 15.gw),
                PopularVideoFilter(
                  filterKey: '综合',
                  dataList: const [],
                  moviesCategory: videoType,
                  selectedItem: state.videoType?.dictValue ?? '',
                  onFilterSelected: (item) => context.read<PopularVideoFilterCubit>().updateVideoTypeFilter(
                        videoType.firstWhere((element) => element.dictValue == item),
                      ),
                ),
                SizedBox(height: 10.gw),
                PopularVideoFilter(
                  filterKey: '地区',
                  dataList: ['热门', ...(tags?.areaCategory ?? [])],
                  selectedItem: state.areaFilter,
                  onFilterSelected: (item) => context.read<PopularVideoFilterCubit>().updateAreaFilter(item),
                ),
                SizedBox(height: 10.gw),
                PopularVideoFilter(
                  filterKey: '类型',
                  dataList: const [],
                  moviesCategory: [
                    VideoHotTagMoviesCategory()
                      ..dictKey = 'all'
                      ..dictValue = '热门',
                    ...(tags?.moviesCategory ?? [])
                  ],
                  selectedItem: state.categoryFilter,
                  onFilterSelected: (item) => context.read<PopularVideoFilterCubit>().updateCategoryFilter(item),
                ),
                SizedBox(height: 10.gw),
                PopularVideoFilter(
                  filterKey: '年份',
                  dataList: ['热门', ...(tags?.videoYear ?? [])],
                  selectedItem: state.yearFilter,
                  onFilterSelected: (item) => context.read<PopularVideoFilterCubit>().updateYearFilter(item),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  double get maxExtent => 150.gw;

  @override
  double get minExtent => 150.gw;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
