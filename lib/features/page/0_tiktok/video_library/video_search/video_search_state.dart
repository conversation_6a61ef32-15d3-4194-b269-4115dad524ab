// video_search_state.dart
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';

class VideoSearchState {
  final List<String> searchHistory;
  final List<String> hotSearches;
  final String currentSearch;
  final bool isSearching;
  NetState netState;
  List<VideoListRecords> videoList;
  int pageNo;
  bool? isNoMoreDataState;

  /// 是否还有更多数据

  VideoSearchState({
    this.searchHistory = const [],
    this.hotSearches = const [],
    this.currentSearch = '',
    this.isSearching = false,
    this.netState = NetState.loading,
    this.videoList = const [],
    this.pageNo = 1,
    this.isNoMoreDataState = false,
  });

  VideoSearchState init() {
    return VideoSearchState(
      searchHistory: [],
      hotSearches: ['热门视频', '搞笑', '舞蹈', '游戏', 'Cosplay', '角色'],
      currentSearch: '',
      isSearching: false,
    );
  }

  VideoSearchState clone({
    List<String>? searchHistory,
    List<String>? hotSearches,
    String? currentSearch,
    bool? isSearching,
    NetState? netState,
    List<VideoListRecords>? videoList,
    int? pageNo,
    bool? isNoMoreDataState,
  }) {
    return VideoSearchState(
      searchHistory: searchHistory ?? this.searchHistory,
      hotSearches: hotSearches ?? this.hotSearches,
      currentSearch: currentSearch ?? this.currentSearch,
      isSearching: isSearching ?? this.isSearching,
      netState: netState ?? this.netState,
      videoList: videoList ?? this.videoList,
      pageNo: pageNo ?? this.pageNo,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
    );
  }
}
