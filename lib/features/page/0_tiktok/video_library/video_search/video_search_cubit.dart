// video_search_cubit.dart
import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/video.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'video_search_state.dart';

class VideoSearchCubit extends Cubit<VideoSearchState> {
  static const String _searchHistoryKey = 'video_search_history';

  VideoSearchCubit() : super(VideoSearchState().init()) {
    _loadSearchHistory(); // 初始化时加载历史记录
  }

  // 加载搜索历史
  Future<void> _loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList(_searchHistoryKey) ?? [];
    emit(state.clone(searchHistory: history));
  }

  // 保存搜索历史
  Future<void> _saveSearchHistory(List<String> history) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_searchHistoryKey, history);
  }

  void updateSearch(String query) {
    emit(state.clone(currentSearch: query));
  }

  Future<void> startSearch(String query) async {
    sl<NavigatorService>().unFocus();
    if (query.isEmpty) return;

    final newHistory = List<String>.from(state.searchHistory);
    if (!newHistory.contains(query)) {
      newHistory.insert(0, query);
      if (newHistory.length > 10) newHistory.removeLast();
      await _saveSearchHistory(newHistory); // 保存到本地
    }

    emit(state.clone(
      searchHistory: newHistory,
      currentSearch: query,
      isSearching: true,
    ));
    updatePageNo(1);
    fetchVideoDataList();
  }

  Future<void> deleteOneHistory(String text) async {
    List<String> tmp = List.from(state.searchHistory);
    tmp.removeWhere((e) => e == text);
    await _saveSearchHistory(tmp);
    emit(state.clone(searchHistory: tmp));
  }

  Future<void> clearHistory() async {
    await _saveSearchHistory([]); // 清除本地存储
    emit(state.clone(searchHistory: []));
  }

  void cancelSearch() {
    emit(state.clone(
      currentSearch: '',
      videoList: [],
      isSearching: false,
    ));
  }

  void updatePageNo(int pageNo) {
    state.pageNo = pageNo;
    emit(state.clone());
  }

  void updatePageNoToNext() {
    state.pageNo += 1;
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    state.isNoMoreDataState = isNoMoreDataState;
    emit(state.clone());
  }

  bool _isLoading = false;

  /// 发送请求
  Future<void> fetchVideoDataList() async {
    // state.netState = NetState.loadingState;
    // emit(state.clone());
    if (_isLoading) return;
    _isLoading = true;

    try {
      VideoListEntity result = await VideoApi.fetchHomeVideoList(
        pageNo: state.pageNo,
        videoCategory: "",
        keyword: state.currentSearch,
      );
      if (result.total <= state.videoList.length) {
        state.isNoMoreDataState = true;
      } else {
        state.isNoMoreDataState = false;
      }
      if (state.pageNo == 1) {
        state.videoList = result.records;
      } else {
        state.videoList = List.from(state.videoList)..addAll(result.records);
      }

      if (state.videoList.isEmpty) {
        state.netState = NetState.empty;
      } else {
        state.netState = NetState.success;
      }
    } catch (_) {
    } finally {
      _isLoading = false;
      emit(state.clone());
    }
  }

  onClickPop() {
    if (state.isSearching) {
      emit(state.clone(isSearching: false));
    } else {
      sl<NavigatorService>().pop();
    }
  }
}
