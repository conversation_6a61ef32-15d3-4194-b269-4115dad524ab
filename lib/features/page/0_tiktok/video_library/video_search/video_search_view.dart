// video_search_view.dart
import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/base/empty_widget.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'package:wd/shared/widgets/tiktok/video_search_bar.dart';
import 'package:wd/shared/widgets/tiktok/video_sliver_grid_view.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/shared/widgets/video/cell/search_history_cell.dart';
import 'video_search_cubit.dart';
import 'video_search_state.dart';

class VideoSearchPage extends BasePage {
  const VideoSearchPage({super.key});

  @override
  BasePageState<BasePage> getState() => _VideoSearchPageState();
}

class _VideoSearchPageState extends BasePageState<VideoSearchPage> {
  final textController = TextEditingController();
  final RefreshController refreshController = RefreshController(initialRefresh: false);


  @override
  void initState() {
    pageTitle = 'search'.tr(); // 搜索
    super.initState();
  }
  void _onRefresh() {
    context.read<VideoSearchCubit>().updatePageNo(1);
    context.read<VideoSearchCubit>().updateIsNoMoreDataState(false);
    context.read<VideoSearchCubit>().fetchVideoDataList();
  }

  void _onLoading() {
    context.read<VideoSearchCubit>().updatePageNoToNext();
    context.read<VideoSearchCubit>().fetchVideoDataList();
  }

  void _listener(BuildContext context, VideoSearchState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  Widget _buildSearchBar() {
    final cubit = context.read<VideoSearchCubit>();
    return VideoSearchBar(
      autofocus: !cubit.state.isSearching,
      onChanged: (value) => cubit.updateSearch(value),
      onSubmitted: (value) => cubit.startSearch(value),
      onTapSearch: () => cubit.startSearch(textController.text),
      onFocusChanged: (hasFocus) {
        if (hasFocus) {
          cubit.cancelSearch();
        }
      },
      controller: textController,
    );
  }


  Widget _buildSectionHeader(String title, String iconPath, {VoidCallback? onClear}) {
    return Row(
      children: [
        SvgPicture.asset(iconPath, height: 16.gw),
        SizedBox(width: 8.gw),
        AneText(
          title,
          style: context.textTheme.secondary.fs16.w500,
        ),
        if (onClear != null) ...[
          const Spacer(),
          InkWell(
            onTap: onClear,
            child: AneText('clean_all'.tr(), style: context.textTheme.title),
          ),
        ],
      ],
    );
  }

  Widget _buildHistoryList(List<String> data) {
    final cubit = BlocProvider.of<VideoSearchCubit>(context);
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final text = data[index];
          return SearchHistoryCell(
            title: data[index],
            type: SearchHistoryCellType.history,
            onTapCell: () {
              textController.text = text;
              cubit.startSearch(text);
            },
            onTapDelete: () {
              cubit.deleteOneHistory(text);
            },
          );
        },
        itemCount: min(data.length, 5));
  }

  Widget _buildTagList(List<String> tags) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: tags.map((tag) => _buildTag(tag)).toList(),
    );
  }

  Widget _buildTag(String tag) {
    final cubit = BlocProvider.of<VideoSearchCubit>(context);
    return  InkWell(
      onTap: () {
        textController.text = tag;
        cubit.startSearch(tag);
      },
      child: Container(
          height: 32.gw,
          padding: EdgeInsets.symmetric(horizontal:  11.gw),
          // padding: 4px 6px 4px 6px;
          decoration: BoxDecoration(
            color: context.colorTheme.borderA,
            borderRadius: BorderRadius.all(Radius.circular(10.gw)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AneText(
                tag,
                style: context.textTheme.title,
              ),
            ],
          )),
    );
  }

  Widget _buildSearchResults(VideoSearchState state) {
    if (state.netState == NetState.empty) {
      return Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: const EmptyWidget(),
      );
    }

    if (state.netState == NetState.loading) {
      return GridView.builder(
        padding: EdgeInsets.only(top: 15.gw, left: 14.gw, right: 14.gw),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 10.gw,
          crossAxisSpacing: 10.gw,
          childAspectRatio: 168 * 94.5,
        ),
        itemCount: 10, // 显示10个骨架项
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 视频缩略图占位
                Expanded(
                  flex: 4,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                  ),
                ),
                // 标题占位
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.all(8.gw),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: double.infinity,
                          height: 12.gw,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        SizedBox(height: 4.gw),
                        Container(
                          width: 100.gw,
                          height: 12.gw,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }

    return CommonRefresher(
      bgColor: Theme.of(context).scaffoldBackgroundColor,
      enablePullDown: false,
      enablePullUp: true,
      refreshController: refreshController,
      onRefresh: null,
      onLoading: _onLoading,
      listWidget: CustomScrollView(
        slivers: [
          // 视频列表
          SliverPadding(
            padding: EdgeInsets.only(top: 15.gw, left: 14.gw, right: 14.gw),
            sliver: VideoSliverGridView(
              paddingH: 14.gw,
              videoList: state.videoList,
              onTapCell: (videoEntity) {
                AuthUtil.checkIfLogin(() {
                  sl<NavigatorService>()
                      .push(AppRouter.videoDetail, arguments: {"model": videoEntity, "videoCategory": ""});
                });
              },
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildSearchHome(BuildContext context, VideoSearchState state) {
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 20.gw),
      children: [
        SizedBox(height: 12.gw),
        _buildSearchBar(),

        if (state.searchHistory.isNotEmpty) ...[
          SizedBox(height: 24.gw),
          // 搜索历史
          _buildSectionHeader(
            'search_history'.tr(),
            "assets/images/tiktok/icon_search_history.svg",
            onClear: () => context.read<VideoSearchCubit>().clearHistory(),
          ),
          _buildHistoryList(state.searchHistory),
        ],
        SizedBox(height: 24.gw),
        // 热门搜索
        _buildSectionHeader('hot_search'.tr(), "assets/images/tiktok/icon_search_hot.svg"),
        SizedBox(height: 10.gw),
        _buildTagList(state.hotSearches),
      ],
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<VideoSearchCubit, VideoSearchState>(
        listener: _listener,
        builder: (context, state) {
          if (state.isSearching) {
            return _buildSearchResults(state);
          }
          return _buildSearchHome(context, state);
        });
  }
}
