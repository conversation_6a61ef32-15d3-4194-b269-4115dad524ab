import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/order/order_main_cell.dart';

import 'order_state.dart';

class OrderCubit extends Cubit<OrderState> {
  OrderCubit() : super(const OrderState());

  fetchListData() async {
    if (!sl<UserCubit>().state.isLogin) return;

    GSEasyLoading.showLoading();
    final result = await ChannelApi.gameAllTypeRecord(type: state.currentDateType);
    GSEasyLoading.dismiss();

    final totalWinAmount = result.map((e) => e.totalWin).reduce((a, b) => a + b);
    final dataList = result.map((e) => GSOrderListViewModel.formOrderMainEntity(e)).toList();

    emit(state.copyWith(
      totalWinAmount:totalWinAmount,
      dataList:dataList,
      netState: NetState.success,
    ));
  }

  void currentDateTypeChanged(RecordDateType type) {
    emit(state.copyWith(currentDateType: type));
    fetchListData();
  }
}
