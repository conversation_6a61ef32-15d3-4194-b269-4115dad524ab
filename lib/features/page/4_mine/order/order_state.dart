import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';

import 'package:equatable/equatable.dart';
import 'package:wd/shared/widgets/order/order_main_cell.dart';

class OrderState extends BaseState<GSOrderListViewModel> with EquatableMixin {
  final double totalWinAmount;
  final RecordDateType currentDateType;

  const OrderState({
    // 只保留用到的 BaseState 字段
    super.netState = NetState.loading,
    super.dataList = const [],

    // 自身字段
    this.totalWinAmount = 0,
    this.currentDateType = RecordDateType.today,
  });

  OrderState copyWith({
    // BaseState
    NetState? netState,
    List<GSOrderListViewModel>? dataList,

    // 自身
    double? totalWinAmount,
    RecordDateType? currentDateType,
  }) {
    return OrderState(
      netState: netState ?? this.netState,
      dataList: dataList ?? this.dataList,
      totalWinAmount: totalWinAmount ?? this.totalWinAmount,
      currentDateType: currentDateType ?? this.currentDateType,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    dataList,
    // 自身
    totalWinAmount,
    currentDateType,
  ];
}

