import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/order/order_main_cell.dart';

import 'channel_type_order_list_state.dart';

class ChannelTypeOrderListCubit extends Cubit<ChannelTypeOrderListState> {
  ChannelTypeOrderListCubit() : super(const ChannelTypeOrderListState());

  Future<void> fetchListData({required String gameClassCode}) async {
    GSEasyLoading.showLoading();
    final result = await ChannelApi.gameOneTypeRecord(
      gameClassCode: gameClassCode,
      type: state.currentDateType,
    );
    GSEasyLoading.dismiss();

    // 映射成 VM 列表
    final items = result.map((e) => GSOrderListViewModel.formOrderPlatformEntity(e)).toList(growable: false);

    // 合计金额（空列表返回 0）
    final totalWin = result.fold<double>(0, (sum, e) => sum + (e.totalWin));

    emit(state.copyWith(
      dataList: items,
      totalWinAmount: totalWin,
      netState: NetState.success,
    ));
  }

  void currentDateTypeChanged(RecordDateType type, String gameClassCode) {
    // 若需要切换时立刻展示 loading，可先 emit 一次
    emit(state.copyWith(currentDateType: type));

    fetchListData(gameClassCode: gameClassCode);
  }
}
