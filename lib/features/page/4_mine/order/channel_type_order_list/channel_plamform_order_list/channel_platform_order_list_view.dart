import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/order/order_sub_cell.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'channel_platform_order_list_cubit.dart';
import 'channel_platform_order_list_state.dart';

class ChannelPlatformOrderListPage extends BasePage {
  final String gameClassCode;
  final String categoryCode;
  final String thirdPlatformId;
  final String gameClassName;
  final RecordDateType dateType;

  const ChannelPlatformOrderListPage({
    super.key,
    required this.gameClassCode,
    required this.categoryCode,
    required this.thirdPlatformId,
    required this.gameClassName,
    required this.dateType,
  });

  @override
  BasePageState<BasePage> getState() => _ChannelPlamformOrderListPageState();
}

class _ChannelPlamformOrderListPageState extends BasePageState<ChannelPlatformOrderListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  Color cardBgColor = const Color(0xFFF7F7F7);

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = widget.gameClassName;
    isNeedEmptyDataWidget = false;

    _tabController = TabController(length: 3, vsync: this, initialIndex: widget.dateType.index)
      ..addListener(() {
        _getData();
      });
    _getData();
  }

  void _onLoading() {
    context.read<ChannelPlatformOrderListCubit>().updatePageNoToNext();
    _getData();
  }

  _getData() {
    context.read<ChannelPlatformOrderListCubit>().currentDateTypeChanged(
          type: RecordDateType.values[_tabController.index],
          categoryCode: widget.categoryCode,
          gameClassCode: widget.gameClassCode,
          thirdPlatformId: widget.thirdPlatformId,
        );
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  Widget mainPageWidget(ChannelPlatformOrderListState state) {
    return GSDateTabBarWidget(
      tabController: _tabController,
      children: [
        if (state.netState == NetState.empty) ...[
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                emptyWidget(),
              ],
            ),
          )
        ],
        if (state.netState == NetState.success) ...[
          Container(
            margin: EdgeInsets.only(top: 10.gw),
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Order Amount Card
                Expanded(
                  child: _buildMetricCard(
                    context: context,
                    title: 'order_amount'.tr(),
                    value: state.totalBetAmount.formattedMoney,
                    valueColor: context.colorTheme.textPrimary,
                  ),
                ),
                SizedBox(width: 16.gw),
                // Total Profit & Loss Card
                Expanded(
                  child: _buildMetricCard(
                    context: context,
                    title: 'total_profit_loss'.tr(),
                    value: state.totalWinAmount.formattedMoney,
                    valueColor: context.colorTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: AnimationLimiter(
              child: CommonRefresher(
                enablePullDown: true,
                enablePullUp: true,
                refreshController: refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                listWidget: ListView.separated(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  itemBuilder: (context, index) => AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: GSOrderSubListCell(model: state.dataList![index]),
                      ),
                    ),
                  ),
                  separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                  itemCount: state.dataList!.length,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _listener(BuildContext context, ChannelPlatformOrderListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<ChannelPlatformOrderListCubit, ChannelPlatformOrderListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }

  Widget _buildMetricCard({
    required BuildContext context,
    required String title,
    required String value,
    required Color valueColor,
  }) {
    return Container(
      width: 192.gw,
      padding: EdgeInsets.all(3.gw),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.gw),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF4F4D4B).withOpacity(0),
            const Color(0xFF2F2E2D),
          ],
          stops: const [0.35, 1.0],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF161616),
          borderRadius: BorderRadius.circular(8.gw),
          border: Border.all(
            color: const Color(0xFFE7DECD).withOpacity(0.1),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFB8B8B6).withOpacity(0.16),
              offset: const Offset(0, -3),
              blurRadius: 6,
            ),
          ],
        ),
        child: Stack(
          children: [
            // Content
            Padding(
              padding: EdgeInsets.all(16.gw),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AneText(
                          title,
                          style: context.textTheme.title,
                        ),
                        SizedBox(height: 16.gw),
                        AneText(
                          value,
                          style: context.textTheme.primary.fs20.w500,
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Decorative gradient lines at bottom
            Positioned(
              bottom: 0,
              left: 4.gw,
              right: 4.gw,
              child: SizedBox(
                height: 6.gw,
                child: CustomPaint(
                  painter: _GradientLinesPainter(),
                  size: Size(179.gw, 12.gw),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom painter for decorative gradient lines
class _GradientLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Create gradient shader
    final gradient = LinearGradient(
      colors: [
        const Color(0xFFFFFFFF).withOpacity(0),
        const Color(0xFFF8F4E9).withOpacity(0.15),
      ],
      stops: const [0.2, 1.0],
    );

    paint.shader = gradient.createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    // Draw vertical lines with spacing
    for (int i = 0; i < 15; i++) {
      final x = i * 12.0;
      if (x < size.width) {
        canvas.drawLine(
          Offset(x, 0),
          Offset(x, size.height),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
