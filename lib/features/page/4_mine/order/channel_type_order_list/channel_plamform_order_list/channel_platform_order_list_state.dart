import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';

import 'package:equatable/equatable.dart';
import 'package:wd/shared/widgets/order/order_sub_cell.dart';

class ChannelPlatformOrderListState extends BaseState<GSOrderSubViewModel> with EquatableMixin {
  final RecordDateType currentDateType;
  final double totalBetAmount;
  final double totalWinAmount;
  final int pageNo;

  const ChannelPlatformOrderListState({
    // 仅保留需要的 BaseState 字段
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.dataList = const [],

    // 自身字段
    this.currentDateType = RecordDateType.today,
    this.totalBetAmount = 0,
    this.totalWinAmount = 0,
    this.pageNo = 1,
  });

  ChannelPlatformOrderListState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<GSOrderSubViewModel>? dataList,

    // 自身
    RecordDateType? currentDateType,
    double? totalBetAmount,
    double? totalWinAmount,
    int? pageNo,
  }) {
    return ChannelPlatformOrderListState(
      // BaseState
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      // 自身
      currentDateType: currentDateType ?? this.currentDateType,
      totalBetAmount: totalBetAmount ?? this.totalBetAmount,
      totalWinAmount: totalWinAmount ?? this.totalWinAmount,
      pageNo: pageNo ?? this.pageNo,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    isNoMoreDataState,
    dataList,
    // 自身
    currentDateType,
    totalBetAmount,
    totalWinAmount,
    pageNo,
  ];
}

