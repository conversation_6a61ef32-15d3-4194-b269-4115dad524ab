import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/channel.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/order/order_sub_cell.dart';

import 'channel_platform_order_list_state.dart';

class ChannelPlatformOrderListCubit extends Cubit<ChannelPlatformOrderListState> {
  ChannelPlatformOrderListCubit() : super(const ChannelPlatformOrderListState());

  static const int pageSize = 20;

  fetchListData({
    required String gameClassCode,
    required String categoryCode,
    required String thirdPlatformId,
  }) async {
    GSEasyLoading.showLoading();
    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final result = await ChannelApi.fetchGameMainPlatformRecord(
      gameClassCode: gameClassCode,
      categoryCode: categoryCode,
      thirdPlatformId: thirdPlatformId,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      pageNo: state.pageNo,
      pageSize: pageSize,
    );
    GSEasyLoading.dismiss();

    if (result == null) {
      emit(state.copyWith(
          totalBetAmount: 0,
        totalWinAmount: 0,
        dataList: [],
        netState: NetState.failed,
      ));
    } else {
      if (result.page.total <= state.dataList!.length) {
        emit(state.copyWith(isNoMoreDataState: true));
      }

      List<GSOrderSubViewModel> dataList;
      if (state.pageNo == 1) {
        dataList = result.page.records.map((e) => GSOrderSubViewModel.formOrderChannelListPageRecord(e)).toList();
      } else {
        dataList = List.from(state.dataList!)
          ..addAll(result.page.records.map((e) => GSOrderSubViewModel.formOrderChannelListPageRecord(e)).toList());
      }

      emit(state.copyWith(
          totalBetAmount:result.totalBetAmount,
        totalWinAmount:result.totalWinToday,
        netState: dataList.isNotEmpty ? NetState.success : NetState.empty,
        dataList: dataList,
      ));
    }
  }

  void currentDateTypeChanged({
    required RecordDateType type,
    required String gameClassCode,
    required String categoryCode,
    required String thirdPlatformId,
  }) {
    if (type != state.currentDateType) {
      emit(state.copyWith(
        currentDateType: type,
        pageNo: 1,
        isNoMoreDataState: false,
        // 如果切换日期想同时清空列表并进入loading，可放开下面两行
        // dataList: const [],
        // netState: NetState.loading,
      ));
    }
    fetchListData(
      gameClassCode: gameClassCode,
      categoryCode: categoryCode,
      thirdPlatformId: thirdPlatformId,
    );
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

}
