import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/platform_amount_entity.dart';
import 'package:wd/core/models/entities/user_info_entity.dart';

import 'package:equatable/equatable.dart';

class MyWalletState extends BaseState<PlatformAmountEntity> with EquatableMixin {
  final List<UserAsset> assetList;
  final bool isFirstFetchingAll;
  final bool isFetchingTransferAll;

  const MyWalletState({
    // 只保留需要的 BaseState 字段
    super.netState = NetState.success, // 你的 init 里就是 success
    super.isNoMoreDataState = false,
    super.dataList = const [],

    // 自己的字段
    this.assetList = const [],
    this.isFirstFetchingAll = true,
    this.isFetchingTransferAll = false,
  });

  MyWalletState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<PlatformAmountEntity>? dataList,

    // 自身
    List<UserAsset>? assetList,
    bool? isFirstFetchingAll,
    bool? isFetchingTransferAll,
  }) {
    return MyWalletState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      assetList: assetList ?? this.assetList,
      isFirstFetchingAll: isFirstFetchingAll ?? this.isFirstFetchingAll,
      isFetchingTransferAll:
      isFetchingTransferAll ?? this.isFetchingTransferAll,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    isNoMoreDataState,
    dataList,
    // 自身
    assetList,
    isFirstFetchingAll,
    isFetchingTransferAll,
  ];
}
