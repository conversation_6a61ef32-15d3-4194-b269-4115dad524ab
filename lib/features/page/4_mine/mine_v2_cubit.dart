import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'video_coupon/video_coupon_cubit.dart';
import 'mine_v2_state.dart';

class MineV2Cubit extends Cubit<MineV2State> {
  late StreamSubscription<Map<String, dynamic>> _subscription;
  bool _isInitialized = false;

  MineV2Cubit() : super(MineV2State()) {
    fetchPromoBanners();
  }


  Future<void> logout() async {
    try {
      GSEasyLoading.showLoading();
      await Future.wait([
        UserApi.doLogout(),
      ]);
    } finally {
      GSEasyLoading.dismiss();

      sl<UserCubit>().setLoginInfo(null);

      _isInitialized = false;
      GameUtil.favGameIdList.clear();
    }
  }

  @override
  Future<void> close() {
    _subscription.cancel();
    return super.close();
  }

  Future<void> fetchPromoBanners() async {
    // MineState newState = state.clone()..promoBannerFetchStatus = NetState.loading;
    // emit(newState);
    //
    // final result = await AccountManagementApi.fetchBanner();
    // if (result.isNotEmpty) {
    //   newState = newState.clone()
    //     ..promoBanners = result
    //     ..promoBannerFetchStatus = NetState.success;
    //   emit(newState);
    // } else {
    //   newState = newState.clone()..promoBannerFetchStatus = NetState.failed;
    //   emit(newState);
    // }
  }

}
