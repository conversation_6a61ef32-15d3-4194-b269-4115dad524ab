import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/common_textformfield.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import 'add_bank_card_cubit.dart';
import 'add_bank_card_state.dart';

class AddBankCardPage extends BasePage {
  const AddBankCardPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddBankCardPageState();
}

class _AddBankCardPageState extends BasePageState<AddBankCardPage> {
  final TextEditingController textControllerName = TextEditingController();
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController();
  final TextEditingController textControllerBankAddress =
      TextEditingController();

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  void initState() {
    pageTitle = "add_bank_card".tr();
    context.read<AddBankCardCubit>().fetchBankListData();

    // 判断是否已设置真实姓名
    final realName = sl<UserCubit>().state.userInfo?.realName;
    if (realName != null && realName.isNotEmpty) {
      textControllerName.text = realName;
    }
    super.initState();
  }

  @override
  void dispose() {
    textControllerName.dispose();
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    textControllerBankAddress.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 12.5.gw, vertical: 24.gw),
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    minHeight: constraints.maxHeight),
                child: IntrinsicHeight(
                  child: Column(
                    children: [
                      Column(
                        children: [
                          _buildFormFieldsSection(),
                          SizedBox(height: 10.gw),
                          _buildDefaultCardSwitchSection(),
                        ],
                      ),
                      const Spacer(),
                      Padding(
                        padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom + 60.gw,
                          top: 20.gw,
                        ),
                        child: _buildSubmitButton(),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }
      ),
    );
  }

  Widget _buildRealNameWidget() {
    // final realName = "阿道夫";

    return BlocSelector<UserCubit, UserState, String?>(
        selector: (state) => state.userInfo?.realName,
        builder: (context, realName) {
          if (realName != null && realName.isNotEmpty) {
            return CommonTextFormField(
              controller: textControllerName,
              validator: (v) => null,
              hintText: "enter_real_name".tr(),
              inputEnable: false,
              readOnly: true,
              prefixIcon: _prefixIcon(Assets.iconProfileUserAlt),
            );
          }

          return Column(
            children: [
              Row(
                children: [
                  Image.asset(
                    Assets.iconLoginShield,
                    width: 30.gw,
                    height: 30.gw,
                  ),
                  SizedBox(width: 5.gw),
                  Expanded(
                    child: AneText(
                      "bind_bank_card_name_notice".tr(),
                      style: context.textTheme.secondary.fs16,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.5.gw),
              CommonTextFormField(
                margin: EdgeInsets.zero,
                controller: textControllerName,
                validator: (v) {
                  if (v != null && StringUtil.containsSpecialChars(v)) {
                    return "no_spaces_special_chars".tr();
                  }

                  return v!.trim().isEmpty ? "name_cannot_be_empty".tr() : null;
                },
                hintText: "enter_real_name".tr(),
                prefixIcon: _prefixIcon(Assets.iconProfileUserAlt),
              ),
            ],
          );
        });
  }

  Widget _buildFormFieldsSection() {
    return CommonCard(
      radius: 12.gw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AneText("add_bank_card".tr(),
              style: context.textTheme.secondary.fs20.w500),
          SizedBox(height: 32.gw),
          _buildRealNameWidget(),
          SizedBox(height: 32.gw),
          CommonTextFormField(
            controller: textControllerBankNum,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "bank_card_number_cannot_be_empty".tr();
              }
              if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                return "no_spaces_special_chars".tr();
              }
              return null;
            },
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(19),
            ],
            hintText: "please_enter_bank_card_number".tr(),
            prefixIcon: _prefixIcon(Assets.iconBankCardv3),
          ),
          SizedBox(height: 32.gw),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => context
                .read<AddBankCardCubit>()
                .onClickShowBankListPicker(
                    context: context, controller: textControllerBankName),
            child: CommonTextFormField(
              controller: textControllerBankName,
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return "please_select_bank_name".tr();
                }
                return null;
              },
              hintText: "please_select_bank_name".tr(),
              prefixIcon: _prefixIcon(Assets.iconProfileUserAlt),
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: 16.gw),
                child: SvgPicture.asset(
                  Assets.iconToolBarArrowDown,
                  width: 12.gw,
                  height: 12.gw,
                ),
              ),
              inputEnable: false,
            ),
          ),
          SizedBox(height: 32.gw),
          CommonTextFormField(
            controller: textControllerBankAddress,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "bank_branch_address_cannot_be_empty".tr();
              }
              if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                return "no_spaces_special_chars".tr();
              }
              return null;
            },
            hintText: "please_enter_bank_branch_address".tr(),
            prefixIcon: _prefixIcon(Assets.iconLocation),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AneText("set_as_default_bank_card".tr(), style: context.textTheme.title.fs16.w500),
          BlocBuilder<AddBankCardCubit, AddBankCardState>(
            builder: (context, state) {
              return CommonSwitch(
                value: state.isDefaultCard,
                onChanged: (value) =>
                    context.read<AddBankCardCubit>().onClickSwitch(value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
        title: 'submit'.tr(),
        onPressed: () {
          sl<NavigatorService>().unFocus();
          if(formKey.currentState?.validate() ?? false) {
            context.read<AddBankCardCubit>().onSubmit(
              context,
              realName: textControllerName.text,
              cardNo: textControllerBankNum.text,
              bankAddress: textControllerBankAddress.text,
            );
          }
        });
  }

  Widget _prefixIcon(String iconName) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: Image.asset(
        iconName,
        width: 20.gw,
        height: 20.gw,
      ),
    );
  }
}
