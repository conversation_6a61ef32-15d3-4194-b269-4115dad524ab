import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/bank_entity.dart';

class AddBankCardState extends BaseState<BankEntity> with EquatableMixin {
  final BankEntity? selectBank;
  final bool isDefaultCard;

  AddBankCardState({
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.isNetWorkFinish = false,
    super.dataList = const [],
    super.netLoadCount = 0,
    GlobalKey<FormState>? formKey,
    FocusNode? funPasswordFocusNode,
    this.selectBank,
    this.isDefaultCard = false,
  });

  /// copyWith 方法
  AddBankCardState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    bool? isNetWorkFinish,
    List<BankEntity>? dataList,
    int? netLoadCount,
    GlobalKey<FormState>? formKey,
    BankEntity? selectBank,
    bool? isDefaultCard,
  }) {
    return AddBankCardState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      isNetWorkFinish: isNetWorkFinish ?? this.isNetWorkFinish,
      dataList: dataList ?? this.dataList,
      netLoadCount: netLoadCount ?? this.netLoadCount,
      selectBank: selectBank ?? this.selectBank,
      isDefaultCard: isDefaultCard ?? this.isDefaultCard,
    );
  }

  @override
  List<Object?> get props => [
        netState,
        isNoMoreDataState,
        isNetWorkFinish,
        dataList,
        netLoadCount,
        selectBank,
        isDefaultCard,
      ];
}
