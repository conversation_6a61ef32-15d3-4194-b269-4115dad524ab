import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/bank_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_picker.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'add_bank_card_state.dart';

class AddBankCardCubit extends Cubit<AddBankCardState> {
  AddBankCardCubit() : super(AddBankCardState());

  bool _isFetching = false;

  fetchBankListData() async {
    if (_isFetching) return;

    _isFetching = true;
    GSEasyLoading.showLoading();
    final list = await UserApi.fetchBankList(WithdrawType.bankCard);
    GSEasyLoading.dismiss();
    _isFetching = false;

    emit(state.copyWith(dataList: list));
  }

  int lastIndex = 0;

  void onClickShowBankListPicker({required BuildContext context, required TextEditingController controller}) async {
    sl<NavigatorService>().unFocus();

    // 如果没数据就去拉取
    if (state.dataList?.isEmpty ?? true) {
      await fetchBankListData();
    }

    // 拉取完还是没数据 → 提示并返回
    if (state.dataList?.isEmpty ?? true) {
      GSEasyLoading.showToast("bank_list_empty".tr());
      return;
    }

    if (!context.mounted) return;

    CommonPicker(
      dataList: (state.dataList as List<BankEntity>).map((e) => e.bankName).toList(),
      index: lastIndex,
      callback: (index) {
        lastIndex = index;
        final selectBank = state.dataList![index];
        controller.text = selectBank.bankName;
        emit(state.copyWith(selectBank: selectBank));
      },
    ).show(context);
  }

  void onClickSwitch(bool value) {
    emit(state.copyWith(isDefaultCard: value));
  }

  void onSubmit(
    context, {
    required String realName,
    required String cardNo,
    required String bankAddress,
  }) async {
    if (sl<UserCubit>().isFundPasswordInputLocked) {
      GSEasyLoading.showToast("pay_pwd_error_limit".tr()); // 支付密码连续错误5次，请1分钟后重试
      return;
    }

    final fundPwd = await FundPwdSheet(context).show();
    if (fundPwd == null) return;

    GSEasyLoading.showLoading();
    final flag = await UserApi.addBankCard(
        type: WithdrawType.bankCard,
        bankCode: state.selectBank!.bankCode,
        bankName: state.selectBank!.bankName,
        cardNo: cardNo,
        bankAddress: bankAddress,
        realName: realName,
        fundPwd: fundPwd);
    if (sl<UserCubit>().state.userInfo!.realName.isEmpty) {
      await sl<UserCubit>().fetchUserInfo();
    }
    GSEasyLoading.dismiss();
    if (flag) {
      sl<UserCubit>().resetFundPasswordErrorLock();
      GSEasyLoading.showToast("wallet_added_successfully".tr()); // 添加成功
      await Future.delayed(const Duration(milliseconds: 500));
      sl<NavigatorService>().pop();
    }
  }
}
