import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'payment_list_state.dart';

class PaymentListCubit extends Cubit<PaymentListState> {
  final PaymentType type;

  PaymentListCubit(this.type) : super(PaymentListState(type: type));

  fetchListData() async {
    GSEasyLoading.showLoading();
    int type = 1; // type 1:银行卡  2:钱包
    int wldType = 0; // wldType 0.正常提现 1.线下USDT
    switch (state.type) {
      case PaymentType.bankCard:
        type = 1;
        wldType = 0;
        break;
      case PaymentType.wallet:
        type = 2;
        wldType = 0;
        break;
      case PaymentType.usdt:
        type = 2;
        wldType = 1;
        break;
    }
    final list = await UserApi.fetchBindBankCardList(type: type, wldType: wldType);
    GSEasyLoading.dismiss();

    emit(state.copyWith(netState: NetState.success, dataList: list));
  }

  void goto(itemModel) {
    // 实现跳转逻辑
  }

  // 添加银行卡
  void onClickAdd() async {
    switch (state.type) {
      case PaymentType.bankCard:
        await sl<NavigatorService>().push(AppRouter.userBankAccountAdd);
        break;
      case PaymentType.wallet:
        await sl<NavigatorService>().push(AppRouter.userPaymentWalletAdd);
        break;
      case PaymentType.usdt:
        await sl<NavigatorService>().push(AppRouter.userPaymentUsdtAdd);
        break;
    }
    fetchListData();
  }
}
