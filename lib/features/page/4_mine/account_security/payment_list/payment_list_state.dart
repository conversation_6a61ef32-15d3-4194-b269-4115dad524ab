// 支付类型枚举
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';

/// 支付方式类型枚举 / Payment type enumeration
enum PaymentType {
  /// 银行卡 / Bank card
  bankCard("bank_card_management", "no_bank_card_found", "add_bank_card"),
  /// USDT地址 / USDT address
  usdt("usdt_address_management", "no_usdt_address_found", "add_address"),
  /// 钱包 / Wallet
  wallet("wallet_management", "no_wallet_found", "add_wallet");

  /// 标题翻译键 / Title translation key
  final String titleKey;
  /// 空状态文本翻译键 / Empty state text translation key
  final String emptyTextKey;
  /// 添加按钮文本翻译键 / Add button text translation key
  final String addButtonTextKey;

  /// 构造函数 / Constructor
  const PaymentType(this.titleKey, this.emptyTextKey, this.addButtonTextKey);

  /// 获取翻译后的标题 / Get translated title
  String get title => titleKey.tr();
  
  /// 获取翻译后的空状态文本 / Get translated empty text
  String get emptyText => emptyTextKey.tr();
  
  /// 获取翻译后的添加按钮文本 / Get translated add button text
  String get addButtonText => addButtonTextKey.tr();
}

class PaymentListState extends BaseState<PaymentCardEntity> with EquatableMixin {
  /// 业务类型（一般不变）
  final PaymentType type;

  /// 构造时直接给出合适的默认值（等价于你原来的 init）
  const PaymentListState({
    required this.type,
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.isNetWorkFinish = false,
    super.dataList = const [],
    super.netLoadCount = 0,
  });

  /// 只暴露需要变动的字段到 copyWith（通常 type 不会变，避免误改）
  PaymentListState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    bool? isNetWorkFinish,
    List<PaymentCardEntity>? dataList,
    int? netLoadCount,
    // 如果你确实需要在运行时切换 type，再把下面这一行放开
    // PaymentType? type,
  }) {
    return PaymentListState(
      // type: type ?? this.type,
      type: this.type,
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      isNetWorkFinish: isNetWorkFinish ?? this.isNetWorkFinish,
      dataList: dataList ?? this.dataList,
      netLoadCount: netLoadCount ?? this.netLoadCount,
    );
  }

  @override
  List<Object?> get props => [
    // 基类字段放进等值比较，确保 UI 正确刷新
    netState,
    isNoMoreDataState,
    isNetWorkFinish,
    dataList,
    netLoadCount,
    // 子类字段
    type,
  ];
}