

import 'package:equatable/equatable.dart';
import 'package:wd/core/models/entities/bank_entity.dart';


class AddWalletState extends Equatable {
  final BankEntity? selectBank;
  final bool isDefaultCard;
  final List<BankEntity> dataList;

  const AddWalletState({
    this.selectBank,
    this.isDefaultCard = false,
    this.dataList = const [],
  });

  AddWalletState copyWith({
    BankEntity? selectBank,
    bool? isDefaultCard,
    List<BankEntity>? dataList,
  }) {
    return AddWalletState(
      selectBank: selectBank ?? this.selectBank,
      isDefaultCard: isDefaultCard ?? this.isDefaultCard,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [
    selectBank,
    isDefaultCard,
    dataList,
  ];
}
