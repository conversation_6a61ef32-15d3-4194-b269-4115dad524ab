import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';

import 'add_usdt_address_state.dart';

class AddUsdtAddressCubit extends Cubit<AddUsdtAddressState> {
  AddUsdtAddressCubit() : super(const AddUsdtAddressState());


  void onClickSwitch(bool value) {
    emit(state.copyWith(isDefaultCard: value));
  }

  void onSubmit(
      context, {
        required String address,
        required String addressProtocol,
      }) async {
      if (sl<UserCubit>().isFundPasswordInputLocked)  {
        GSEasyLoading.showToast("pay_pwd_error_limit".tr()); // 支付密码连续错误5次，请1分钟后重试
        return;
      }

      final fundPwd = await FundPwdSheet(context).show();
      if (fundPwd == null) return;

      GSEasyLoading.showLoading();
      final flag = await UserApi.addBankCard(
          type: WithdrawType.wallet,
          bankCode: "USDT",
          bankName: addressProtocol,
          cardNo: address,
          realName: sl<UserCubit>().state.userInfo?.realName ?? "",
          fundPwd: fundPwd,
          wldType: 1, // USDT钱包
      );
      if (sl<UserCubit>().state.userInfo!.realName.isEmpty) {
        await sl<UserCubit>().fetchUserInfo();
      }
      GSEasyLoading.dismiss();
      if (flag) {
        sl<UserCubit>().resetFundPasswordErrorLock();
        GSEasyLoading.showToast("wallet_added_successfully".tr());
        await Future.delayed(const Duration(milliseconds: 500));
        sl<NavigatorService>().pop();
      }

  }

}
