import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/common_textformfield.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'add_usdt_address_cubit.dart';
import 'add_usdt_address_state.dart';

class AddUsdtAddressPage extends BasePage {
  const AddUsdtAddressPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddUsdtAddressPageState();
}

class _AddUsdtAddressPageState extends BasePageState<AddUsdtAddressPage> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController(text: "TRC20");

  @override
  void initState() {
    pageTitle = "add_usdt_address".tr();

    super.initState();
  }

  @override
  void dispose() {
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    super.dispose();
  }

  // 粘贴地址
  Future<void> pasteAddress() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      final text = clipboardData?.text;
      if (text != null && text.isNotEmpty) {
        textControllerBankNum.text = clipboardData!.text!;
      }
    } catch (e) {
      GSEasyLoading.showToast('粘贴失败'.tr());
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Form(
      key: formKey,
      autovalidateMode: AutovalidateMode.disabled,
      child: LayoutBuilder(builder: (context, constraints) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 12.5.gw, vertical: 24.gw),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(minHeight: constraints.maxHeight),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    Column(
                      children: [
                        _buildFormFieldsSection(),
                        SizedBox(height: 10.gw),
                        _buildDefaultCardSwitchSection(),
                      ],
                    ),
                    const Spacer(),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 60.gw,
                        top: 20.gw,
                      ),
                      child: _buildSubmitButton(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AneText("set_as_default_usdt_address".tr(), style: context.textTheme.title.fs16.w500),
          BlocBuilder<AddUsdtAddressCubit, AddUsdtAddressState>(
            builder: (context, state) {
              return CommonSwitch(
                value: state.isDefaultCard,
                onChanged: (value) => context.read<AddUsdtAddressCubit>().onClickSwitch(value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFormFieldsSection() {
    return CommonCard(
      radius: 12.gw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AneText("add_usdt_address".tr(), style: context.textTheme.secondary.fs20.w500),
          SizedBox(height: 32.gw),
          CommonTextFormField(
            controller: textControllerBankNum,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "withdrawal_address_cannot_be_empty".tr();
              }
              if (!StringUtil.validateTRC20Address(v)) {
                return "please_enter_correct_trc20_address".tr();
              }
              return null;
            },
            hintText: "please_enter_withdrawal_address".tr(),
            prefixIcon: _prefixIcon(Assets.iconWithdrawAddress),
          ),
          SizedBox(height: 32.gw),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: CommonTextFormField(
              controller: textControllerBankName,
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return "c".tr();
                }
                if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                  return "no_spaces_special_chars".tr();
                }
                return null;
              },
              hintText: "address_protocol".tr(),
              inputEnable: false,
              prefixIcon: _prefixIcon(Assets.iconWithdrawProtocol),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
        title: 'submit'.tr(),
        onPressed: () {
          sl<NavigatorService>().unFocus();
          if (formKey.currentState?.validate() ?? false) {
            context.read<AddUsdtAddressCubit>().onSubmit(
                  context,
                  address: textControllerBankNum.text,
                  addressProtocol: textControllerBankName.text,
                );
          }
        });
  }

  Widget _prefixIcon(String iconName) {
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: Image.asset(
        iconName,
        width: 20.gw,
        height: 20.gw,
      ),
    );
  }
}
