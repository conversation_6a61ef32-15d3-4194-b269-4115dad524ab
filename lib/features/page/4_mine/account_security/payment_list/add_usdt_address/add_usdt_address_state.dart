import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';

class AddUsdtAddressState extends BaseState with EquatableMixin {
  final bool isDefaultCard;

  const AddUsdtAddressState({
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.isNetWorkFinish = false,
    super.dataList = const [],
    super.netLoadCount = 0,
    this.isDefaultCard = false,
  });

  /// copyWith 方法
  AddUsdtAddressState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    bool? isNetWorkFinish,
    List<dynamic>? dataList,
    int? netLoadCount,
    bool? isDefaultCard,
  }) {
    return AddUsdtAddressState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      isNetWorkFinish: isNetWorkFinish ?? this.isNetWorkFinish,
      dataList: dataList ?? this.dataList,
      netLoadCount: netLoadCount ?? this.netLoadCount,
      isDefaultCard: isDefaultCard ?? this.isDefaultCard,
    );
  }

  @override
  List<Object?> get props => [
    netState,
    isNoMoreDataState,
    isNetWorkFinish,
    dataList,
    netLoadCount,
    isDefaultCard,
  ];
}
