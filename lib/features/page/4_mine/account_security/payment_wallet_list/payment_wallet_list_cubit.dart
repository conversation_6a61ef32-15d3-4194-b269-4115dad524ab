
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';
import 'package:wd/features/page/4_mine/account_security/payment_wallet_list/payment_wallet_list_state.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

class PaymentWalletListCubit extends Cubit<PaymentWalletListState> {
  PaymentWalletListCubit() : super(const PaymentWalletListState());

  fetchListData() async {
    GSEasyLoading.showLoading();
    final list = await UserApi.fetchBindBankCardList(type: 2);
    GSEasyLoading.dismiss();

    emit(state.copyWith(netState: NetState.success, dataList: list));
  }

  void goto(PaymentCardEntity account) {
    // 实现跳转逻辑
  }

  // 添加钱包
  void onClickAdd() async {
    await sl<NavigatorService>().push(AppRouter.userPaymentWalletAdd);
    fetchListData();
  }
}
