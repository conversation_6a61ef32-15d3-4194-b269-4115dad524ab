
import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';

// 定义状态
class PaymentWalletListState extends BaseState<PaymentCardEntity> with EquatableMixin {
  const PaymentWalletListState({
    super.netState = NetState.loading,
    super.dataList = const [],
  });

  PaymentWalletListState copyWith({
    NetState? netState,
    List<PaymentCardEntity>? dataList,
  }) {
    return PaymentWalletListState(
      netState: netState ?? this.netState,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [netState, dataList];
}
