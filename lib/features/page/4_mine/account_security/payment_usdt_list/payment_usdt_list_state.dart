

import 'package:wd/core/base/base_state.dart';

import 'package:equatable/equatable.dart';
import 'package:wd/core/models/entities/payment_card_entity.dart';

class PaymentUsdtListState extends BaseState<PaymentCardEntity> with EquatableMixin {
  const PaymentUsdtListState({
    super.netState = NetState.loading,
    super.dataList = const [],
  });

  PaymentUsdtListState copyWith({
    NetState? netState,
    List<PaymentCardEntity>? dataList,
  }) {
    return PaymentUsdtListState(
      netState: netState ?? this.netState,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [netState, dataList];
}

