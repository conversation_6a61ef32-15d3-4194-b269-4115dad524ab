import 'package:wd/core/base/base_state.dart';


import 'package:equatable/equatable.dart';

class AccountSecurityState extends BaseState<dynamic> with EquatableMixin {
  final String? nickName;
  final String? account;
  final String? phone;
  final String? email;

  const AccountSecurityState({
    this.nickName,
    this.account,
    this.phone,
    this.email,
    super.netState = NetState.loading,
  });

  AccountSecurityState copyWith({
    String? nickName,
    String? account,
    String? phone,
    String? email,
    NetState? netState,
  }) {
    return AccountSecurityState(
      nickName: nickName ?? this.nickName,
      account: account ?? this.account,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      netState: netState ?? this.netState,
    );
  }

  @override
  List<Object?> get props => [
    nickName,
    account,
    phone,
    email,
    netState,
  ];
}

