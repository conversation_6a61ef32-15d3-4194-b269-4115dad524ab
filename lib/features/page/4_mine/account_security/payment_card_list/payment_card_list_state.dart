import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';

// 定义状态
class PaymentCardListState extends BaseState with EquatableMixin {
  const PaymentCardListState({
    super.netState,
    super.isNoMoreDataState,
    super.isNetWorkFinish,
    super.dataList,
    super.netLoadCount,
  });

  /// copyWith 方法
  PaymentCardListState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    bool? isNetWorkFinish,
    List<dynamic>? dataList,
    int? netLoadCount,
  }) {
    return PaymentCardListState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      isNetWorkFinish: isNetWorkFinish ?? this.isNetWorkFinish,
      dataList: dataList ?? this.dataList,
      netLoadCount: netLoadCount ?? this.netLoadCount,
    );
  }

  @override
  List<Object?> get props => [
    netState,
    isNoMoreDataState,
    isNetWorkFinish,
    dataList,
    netLoadCount,
  ];
}
