import 'package:flutter/cupertino.dart';
import 'package:wd/core/base/base_state.dart';

class ModifyPwdState extends BaseState {
  late String oldPwd;
  late String newPwd;
  late String newPwdConfirm;
  late bool isOldPwdVisible;
  late bool isNewPwdVisible;
  late bool isNewPwdConfirmVisible;
  late TextEditingController oldPwdController;
  late TextEditingController newPwdController;
  late TextEditingController newPwdConfirmController;

  ModifyPwdState({
    String? oldPwd,
    String? newPwd,
    String? newPwdConfirm,
    bool? isOldPwdVisible,
    bool? isNewPwdVisible,
    bool? isNewPwdConfirmVisible,
    TextEditingController? oldPwdController,
    TextEditingController? newPwdController,
    TextEditingController? newPwdConfirmController,
    super.netState,
  }) {
    this.oldPwd = oldPwd ?? '';
    this.newPwd = newPwd ?? '';
    this.newPwdConfirm = newPwdConfirm ?? '';
    this.isOldPwdVisible = isOldPwdVisible ?? false;
    this.isNewPwdVisible = isNewPwdVisible ?? false;
    this.isNewPwdConfirmVisible = isNewPwdConfirmVisible ?? false;
    this.oldPwdController = oldPwdController ?? TextEditingController();
    this.newPwdController = newPwdController ?? TextEditingController();
    this.newPwdConfirmController = newPwdConfirmController ?? TextEditingController();
  }

  ModifyPwdState copyWith({
    String? oldPwd,
    String? newPwd,
    String? newPwdConfirm,
    bool? isOldPwdVisible,
    bool? isNewPwdVisible,
    bool? isNewPwdConfirmVisible,
    TextEditingController? oldPwdController,
    TextEditingController? newPwdController,
    TextEditingController? newPwdConfirmController,
    NetState? netState,
  }) {
    return ModifyPwdState(
      oldPwd: oldPwd ?? this.oldPwd,
      newPwd: newPwd ?? this.newPwd,
      newPwdConfirm: newPwdConfirm ?? this.newPwdConfirm,
      isOldPwdVisible: isOldPwdVisible ?? this.isOldPwdVisible,
      isNewPwdVisible: isNewPwdVisible ?? this.isNewPwdVisible,
      isNewPwdConfirmVisible: isNewPwdConfirmVisible ?? this.isNewPwdConfirmVisible,
      oldPwdController: oldPwdController ?? this.oldPwdController,
      newPwdController: newPwdController ?? this.newPwdController,
      newPwdConfirmController: newPwdConfirmController ?? this.newPwdConfirmController,
      netState: netState ?? this.netState,
    );
  }

  ModifyPwdState init() {
    return ModifyPwdState();
  }
}
