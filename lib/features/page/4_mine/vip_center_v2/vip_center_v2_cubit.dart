import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/account_managment.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';

import 'vip_center_v2_state.dart';

class VipCenterV2Cubit extends Cubit<VipCenterV2State> {
  VipCenterV2Cubit() : super(const VipCenterV2State()) {
    fetchVipInfo();
  }

  fetchVipInfo() async {
    final cubit = sl<UserCubit>();
    var vipInfo = cubit.state.vipInfo;

    // 如果未加载则请求
    if (vipInfo == null) {
      await cubit.fetchUserVip();
      vipInfo = cubit.state.vipInfo;
    }

    // 设置当前 index
    if (vipInfo != null) {
      final index = max(vipInfo.vipLevel - 1, 0);
      onChangeCurrentIndex(index);
    }

    // 拉取 VIP 等级列表
    fetchVipList();
  }

  onChangeCurrentIndex(int index) {
    emit(state.copyWith(currentIndex: index));
  }

  void fetchVipList() async {
    try {
      final result = await AccountManagementApi.fetchVipList();

      emit(state.copyWith(
        netState: result.isNotEmpty ? NetState.success : NetState.empty,
        dataList: result,
      ));
    } catch (e) {
      emit(state.copyWith(netState: NetState.showReload));
    }

  }
}
