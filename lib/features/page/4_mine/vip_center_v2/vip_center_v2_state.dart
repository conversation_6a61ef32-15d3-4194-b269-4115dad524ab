
import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/vip_model_entity.dart';

class VipCenterV2State extends BaseState<VipModel> with EquatableMixin{
  final int currentIndex;

 const VipCenterV2State({
    this.currentIndex = 0,
    super.netState,
    super.dataList,
  });

  VipCenterV2State copyWith({
    int? currentIndex,
    NetState? netState,
    List<VipModel>? dataList,
  }) {
    return VipCenterV2State(
      currentIndex: currentIndex ?? this.currentIndex,
      netState: netState ?? this.netState,
      dataList: dataList ?? this.dataList,
    );
  }

  @override
  List<Object?> get props => [
    currentIndex,
    netState,
    dataList,
  ];
}
