import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/agent.dart';
import 'package:wd/core/models/entities/commission_record_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';
import 'rebate_state.dart';

class RebateCubit extends Cubit<RebateState> {
  static const int pageSize = 10;

  RebateCubit() : super(RebateState()) {
    initialize();
  }

  void initialize() async {
    if (sl<UserCubit>().state.inviteInfo == null) await sl<UserCubit>().fetchInviteInfo();

    await Future.wait([fetchSubordinateInfo(), fetchCommissionRecords()]);
  }

  Future<void> fetchSubordinateInfo() async {
    if (!sl<UserCubit>().state.isLogin) return;

    final result = await AgentApi.fetchSubordinateInfo();
    if (result != null) {
      emit(state.copyWith(subordinateCount: result.subordinateCount, commissionEarned: result.commissionEarned));
    }
  }

  Future<void> fetchCommissionRecords({bool refresh = false}) async {
    if (!sl<UserCubit>().state.isLogin) return;

    if (refresh) {
      emit(state.copyWith(currentPage: 1, commissionRecords: []));
    }

    if (state.isLoadingMore || (!refresh && state.currentPage > state.totalPages)) {
      return;
    }

    emit(state.copyWith(isLoadingMore: true));

    final result = await AgentApi.fetchCommissionRecords(
      pageNo: state.currentPage,
      pageSize: pageSize,
    );

    emit(state.copyWith(isLoadingMore: false));
    if (result != null) {
      List<CommissionRecordList> records;
      if (refresh) {
        records = result.records;
      } else {
        records = List.from(state.commissionRecords)..addAll(result.records);
      }

      emit(state.copyWith(
        commissionRecords: records,
        currentPage: state.currentPage + 1,
        totalPages: result.pages,
      ));
    }

    emit(state.copyWith(isLoadingMore: false));
  }
}
