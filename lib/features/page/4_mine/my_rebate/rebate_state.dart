import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/commission_record_entity.dart';

import 'package:equatable/equatable.dart';

class RebateState extends BaseState<dynamic> with EquatableMixin {
  final int subordinateCount;
  final double commissionEarned;
  final List<CommissionRecordList> commissionRecords;
  final int currentPage;
  final int totalPages;
  final bool isLoadingMore;

  const RebateState({
    this.subordinateCount = 0,
    this.commissionEarned = 0,
    this.commissionRecords = const [],
    this.currentPage = 1,
    this.totalPages = 1,
    this.isLoadingMore = false,
    super.netState = NetState.loading, // ✅ 只保留你用到的
  });

  RebateState copyWith({
    int? subordinateCount,
    double? commissionEarned,
    List<CommissionRecordList>? commissionRecords,
    int? currentPage,
    int? totalPages,
    bool? isLoadingMore,
    NetState? netState,
  }) {
    return RebateState(
      subordinateCount: subordinateCount ?? this.subordinateCount,
      commissionEarned: commissionEarned ?? this.commissionEarned,
      commissionRecords: commissionRecords ?? this.commissionRecords,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      netState: netState ?? this.netState,
    );
  }

  @override
  List<Object?> get props => [
    subordinateCount,
    commissionEarned,
    commissionRecords,
    currentPage,
    totalPages,
    isLoadingMore,
    netState,
  ];
}

