import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/shared/widgets/statement/statement_cell.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'package:equatable/equatable.dart';


class StatementState extends BaseState<GSStateListCellModel> with EquatableMixin {
  final List<TransactFilterItem> filterWayList;
  final List<TransactFilterItem> filterTypeList;
  final int pageNo;
  final RecordDateType currentDateType;

  const StatementState({
    // 仅保留需要的 BaseState 字段
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.dataList = const [],

    // 自身字段
    this.filterWayList = const [],
    this.filterTypeList = const [],
    this.pageNo = 1,
    this.currentDateType = RecordDateType.today,
  });

  StatementState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<GSStateListCellModel>? dataList,

    // 自身
    List<TransactFilterItem>? filterWayList,
    List<TransactFilterItem>? filterTypeList,
    int? pageNo,
    RecordDateType? currentDateType,
  }) {
    return StatementState(
      // BaseState
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      // 自身
      filterWayList: filterWayList ?? this.filterWayList,
      filterTypeList: filterTypeList ?? this.filterTypeList,
      pageNo: pageNo ?? this.pageNo,
      currentDateType: currentDateType ?? this.currentDateType,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    isNoMoreDataState,
    dataList,
    // 自身
    filterWayList,
    filterTypeList,
    pageNo,
    currentDateType,
  ];
}

