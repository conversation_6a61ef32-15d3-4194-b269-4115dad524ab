import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../../core/base/base_state.dart';
import '../../../../../../core/models/apis/user.dart';
import '../../../../../../core/models/country.dart';
import '../../../../../../core/services/country_service.dart';
import '../../../../../../shared/widgets/easy_loading.dart';
import '../../../../login/login_state.dart';

part 'phone_update_state.dart';

class PhoneUpdateCubit extends Cubit<PhoneUpdateState> {
  PhoneUpdateCubit() : super(const PhoneUpdateState(phoneNo: '')) {
    _initializeCountry();
  }

  /// Initialize default country
  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    emit(state.copyWith(selectedCountry: defaultCountry));
  }

  /// Update selected country
  void updateSelectedCountry(Country country) {
    emit(state.copyWith(selectedCountry: country));
  }

  void setPhoneNo(String phoneNo) => emit(state.copyWith(phoneNo: phoneNo));

  Future<void> updatePhoneNo({
    required String oldPhoneSmsCode,
    required String newPhoneNo,
    required String newPhoneSmsCode,
  }) async {
    emit(state.copyWith(updateStatus: NetState.loading));
    GSEasyLoading.showLoading();

    final success = await UserApi.updatePhoneNo(
      oldPhoneSmsCode,
      newPhoneNo,
      newPhoneSmsCode,
      areaCode: state.selectedCountry?.areaCode,
    );

    if (success) {
      GSEasyLoading.showToast('act_operation_success'.tr());
      emit(state.copyWith(updateStatus: NetState.success));
    } else {
      emit(state.copyWith(updateStatus: NetState.failed));
    }

    GSEasyLoading.dismiss();
  }

  Future<void> bindPhoneNo({required String phoneNo, required String code}) async {
    emit(state.copyWith(updateStatus: NetState.loading));
    GSEasyLoading.showLoading();

    final success = await UserApi.bindPhoneNo(
      phoneNo,
      code,
      areaCode: state.selectedCountry?.areaCode,
    );

    if (success) {
      GSEasyLoading.showToast('act_operation_success'.tr());
      emit(state.copyWith(updateStatus: NetState.success));
    } else {
      emit(state.copyWith(updateStatus: NetState.failed));
    }

    GSEasyLoading.dismiss();
  }
}
