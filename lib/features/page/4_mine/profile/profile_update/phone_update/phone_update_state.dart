part of 'phone_update_cubit.dart';

class PhoneUpdateState extends Equatable {
  final CaptchaType captchaType;
  final NetState updateStatus;
  final String phoneNo;
  final Country? selectedCountry;
  const PhoneUpdateState({
    this.captchaType = CaptchaType.wangYi,
    this.updateStatus = NetState.idle,
    this.phoneNo = '',
    this.selectedCountry,
  });

  @override
  List<Object?> get props => [captchaType, updateStatus, phoneNo, selectedCountry];

  PhoneUpdateState copyWith({
    CaptchaType? captchaType,
    NetState? updateStatus,
    String? phoneNo,
    Country? selectedCountry,
  }) {
    return PhoneUpdateState(
      captchaType: captchaType ?? this.captchaType,
      updateStatus: updateStatus ?? this.updateStatus,
      phoneNo: phoneNo ?? this.phoneNo,
      selectedCountry: selectedCountry ?? this.selectedCountry,
    );
  }
}
