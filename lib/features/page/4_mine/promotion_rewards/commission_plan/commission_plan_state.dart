part of 'commission_plan_cubit.dart';

class CommissionPlanState extends Equatable {
  final CommissionRechargeEntity? commissionRechargeEntity;
  final NetState commissionRechargeNetState;
  final CommissionBetEntity? commissionBetEntity;
  final NetState commissionBetNetState;
  final CommissionBetList? selectedCommissionBetList;
  final String? error;
  const CommissionPlanState({
    this.commissionRechargeEntity,
    this.commissionRechargeNetState = NetState.idle,
    this.commissionBetEntity,
    this.commissionBetNetState = NetState.idle,
    this.selectedCommissionBetList,
    this.error,
  });

  @override
  List<Object?> get props => [
        commissionRechargeEntity,
        commissionRechargeNetState,
        commissionBetEntity,
        commissionBetNetState,
        selectedCommissionBetList,
        error
      ];

  CommissionPlanState copyWith({
    CommissionRechargeEntity? commissionRechargeEntity,
    NetState? commissionRechargeNetState,
    CommissionBetEntity? commissionBetEntity,
    NetState? commissionBetNetState,
    CommissionBetList? selectedCommissionBetList,
    NetState? commissionReceiveState,
    String? error,
  }) {
    return CommissionPlanState(
      commissionRechargeEntity: commissionRechargeEntity ?? this.commissionRechargeEntity,
      commissionRechargeNetState: commissionRechargeNetState ?? this.commissionRechargeNetState,
      commissionBetEntity: commissionBetEntity ?? this.commissionBetEntity,
      commissionBetNetState: commissionBetNetState ?? this.commissionBetNetState,
      selectedCommissionBetList: selectedCommissionBetList ?? this.selectedCommissionBetList,
      error: error ?? this.error,
    );
  }
}
