import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/commission_bet_entity.dart';
import '../../../../../core/models/entities/commission_recharge_entity.dart';
import '../../../../../shared/widgets/easy_loading.dart';

part 'commission_plan_state.dart';

class CommissionPlanCubit extends Cubit<CommissionPlanState> {
  CommissionPlanCubit() : super(const CommissionPlanState());

  Future<void> fetchCommissionPlanRecharge() async {
    emit(state.copyWith(commissionRechargeNetState: NetState.loading));
    try {
      final commissionRechargeEntity = await PromotionApi.fetchCommissionPlan();
      if (commissionRechargeEntity != null) {
        emit(state.copyWith(
          commissionRechargeEntity: commissionRechargeEntity,
          commissionRechargeNetState: NetState.success,
        ));
      } else {
        emit(state.copyWith(
          commissionRechargeNetState: NetState.showReload,
        ));
      }
    } on Error catch (e) {
      emit(state.copyWith(
        commissionRechargeNetState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  Future<void> fetchPromotionBetConfig() async {
    emit(state.copyWith(commissionBetNetState: NetState.loading));
    GSEasyLoading.showLoading();

    try {
      final commissionBetEntity = await PromotionApi.fetchPromotionBetConfig();
      if (commissionBetEntity != null) {
        emit(state.copyWith(
          commissionBetEntity: commissionBetEntity,
          commissionBetNetState: NetState.success,
          selectedCommissionBetList: commissionBetEntity.list?.first,
        ));
        GSEasyLoading.dismiss();
      } else {
        emit(state.copyWith(
          commissionBetNetState: NetState.showReload,
        ));
        GSEasyLoading.dismiss();
      }
    } catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        commissionBetNetState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  void filterCommissionBetList(String type) {
    final commissionBetList = state.commissionBetEntity?.list?.where((element) => element.type == type).toList();
    emit(state.copyWith(selectedCommissionBetList: commissionBetList?.first));
  }
}
