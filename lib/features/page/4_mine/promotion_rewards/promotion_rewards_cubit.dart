// lib/features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/utils/global_config.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/models/apis/promotion.dart';
import '../../../../shared/widgets/easy_loading.dart';
import 'promotion_rewards_state.dart';

class PromotionRewardsCubit extends Cubit<PromotionRewardsState> {
  PromotionRewardsCubit() : super(const PromotionRewardsState()) {
    fetchRulePicture();
  }

  void changeTeamLevelIndex(int index) {
    emit(state.copyWith(currentTeamLevelIndex: index));
  }

  void changeTabIndex(int index) {
    emit(state.copyWith(currentTabIndex: index));
  }

  void didPop() {
    emit(state.copyWith(currentTabIndex: 0, currentTeamLevelIndex: 0));
  }

  void fetchRulePicture() async {
    final res = await GlobalConfig().getPictureConfig();
    if (res != null) {
      emit(state.copyWith(rulePicture: res.agentPromotionUrl));
    }
  }

  void fetchTeam() async {
    try {
      final teamEntity = await PromotionApi.fetchTeam();
      if (teamEntity != null) {
        emit(state.copyWith(
          teamEntity: teamEntity,
          currentTeamLevelIndex: teamEntity.userTeamLevel ?? 0,
          teamNetState: NetState.success,
        ));
      } else {
        emit(state.copyWith(
          teamNetState: NetState.showReload,
        ));
      }
    } on Error catch (e) {
      emit(state.copyWith(
        teamNetState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  void fetchMyTeam() async {
    GSEasyLoading.showLoading();
    try {
      final myTeamEntity = await PromotionApi.fetchMyTeam();
      if (myTeamEntity != null) {
        emit(state.copyWith(
          myTeamEntity: myTeamEntity,
          myTeamNetState: NetState.success,
        ));
        GSEasyLoading.dismiss();
      } else {
        GSEasyLoading.dismiss();
        emit(state.copyWith(
          myTeamNetState: NetState.showReload,
        ));
      }
    } on Error catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        myTeamNetState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  void fetchCommission() async {
    try {
      final commissionOverviewEntity = await PromotionApi.fetchCommission();
      if (commissionOverviewEntity != null) {
        emit(state.copyWith(
          commissionOverviewEntity: commissionOverviewEntity,
          commissionOverviewNetState: NetState.success,
        ));
      } else {
        emit(state.copyWith(commissionOverviewNetState: NetState.showReload));
      }
    } on Error catch (e) {
      emit(state.copyWith(
        commissionOverviewNetState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  Future<void> commissionReceive() async {
    emit(state.copyWith(commissionReceiveState: NetState.loading));
    GSEasyLoading.showLoading();

    try {
      final flag = await PromotionApi.commissionReceive();
      if (flag != null) {
        emit(state.copyWith(
          commissionReceiveState: NetState.success,
        ));
        GSEasyLoading.dismiss();
      } else {
        emit(state.copyWith(
          commissionReceiveState: NetState.showReload,
        ));
        GSEasyLoading.dismiss();
      }
    } catch (e) {
      GSEasyLoading.dismiss();
      emit(state.copyWith(
        commissionReceiveState: NetState.showReload,
        error: e.toString(),
      ));
    }
  }

  Future<void> fetchUserInviteLink() async {
    emit(state.copyWith(userInviteLinkNetState: NetState.loading));
    try {
      final link = await PromotionApi.fetchUserInviteLink();
      if (link != null) {
        emit(state.copyWith(userInviteLink: link));
      } else {
        emit(state.copyWith(userInviteLinkNetState: NetState.showReload));
      }
    } catch (e) {
      emit(state.copyWith(userInviteLinkNetState: NetState.showReload, error: e.toString()));
    }
  }
}
