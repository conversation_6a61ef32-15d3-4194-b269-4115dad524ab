part of 'team_management_cubit.dart';

class TeamManagementState extends Equatable {
  final TeamMembersEntity? teamMembersEntity;
  final NetState teamMembersNetState;
  final String? error;
  final int pageNo;
  final bool isNoMoreData;
  final TeamDetailsEntity? teamDetailsEntity;
  final NetState teamDetailsNetState;
  final TeamManagementTab? tab;
  const TeamManagementState({
    this.teamMembersEntity,
    this.teamMembersNetState = NetState.idle,
    this.error,
    this.pageNo = 1,
    this.isNoMoreData = false,
    this.teamDetailsEntity,
    this.teamDetailsNetState = NetState.idle,
    this.tab = TeamManagementTab.member,
  });

  @override
  List<Object?> get props => [
        teamMembersEntity,
        teamMembersNetState,
        error,
        teamDetailsEntity,
        teamDetailsNetState,
        pageNo,
        isNoMoreData,
        tab,
      ];

  TeamManagementState copyWith({
    TeamMembersEntity? teamMembersEntity,
    NetState? teamMembersNetState,
    String? error,
    int? pageNo,
    bool? isNoMoreData,
    TeamDetailsEntity? teamDetailsEntity,
    NetState? teamDetailsNetState,
    bool resetTeamDetailsEntity = false,
    bool resetTeamMembersEntity = false,
    TeamManagementTab? tab,
  }) {
    return TeamManagementState(
      teamMembersEntity: resetTeamMembersEntity ? null : teamMembersEntity ?? this.teamMembersEntity,
      teamMembersNetState: teamMembersNetState ?? this.teamMembersNetState,
      error: error ?? this.error,
      pageNo: pageNo ?? this.pageNo,
      isNoMoreData: isNoMoreData ?? this.isNoMoreData,
      teamDetailsEntity: resetTeamDetailsEntity ? null : teamDetailsEntity ?? this.teamDetailsEntity,
      teamDetailsNetState: teamDetailsNetState ?? this.teamDetailsNetState,
      tab: tab ?? this.tab,
    );
  }
}
