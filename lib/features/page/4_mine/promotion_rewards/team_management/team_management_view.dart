import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/utils/extentions.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/team_management/team_management_cubit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../core/base/base_state.dart';
import '../../../../../core/base/common_refresher.dart';
import '../../../../../core/constants/assets.dart';
import '../../../../../core/models/apis/promotion.dart';
import '../../../../../core/models/entities/team_details_entity.dart';
import '../../../../../core/models/entities/team_members_entity.dart';
import '../../../../../core/theme/themes.dart';
import '../../../../../injection_container.dart';
import '../../../../../shared/widgets/common_table.dart';
import '../../../../routers/navigator_utils.dart';
import '../../../../../core/utils/time_util.dart';

class TeamManagementView extends StatefulWidget {
  const TeamManagementView({super.key});

  @override
  State<TeamManagementView> createState() => _TeamManagementViewState();
}

class _TeamManagementViewState extends State<TeamManagementView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    final cubit = context.read<TeamManagementCubit>();
    _tabController = TabController(length: 3, vsync: this, initialIndex: _indexFromTab(cubit.state.tab));
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) return;
      final newTab = _tabFromIndex(_tabController.index);
      if (newTab != cubit.state.tab) {
        cubit.updateTab(newTab);
        _loadDataForTab(newTab, cubit);
      }
    });
  }

  TeamManagementTab _tabFromIndex(int index) {
    switch (index) {
      case 0:
        return TeamManagementTab.member;
      case 1:
        return TeamManagementTab.bet;
      case 2:
        return TeamManagementTab.profit;
      default:
        return TeamManagementTab.member;
    }
  }

  int _indexFromTab(TeamManagementTab? tab) {
    switch (tab) {
      case TeamManagementTab.member:
        return 0;
      case TeamManagementTab.bet:
        return 1;
      case TeamManagementTab.profit:
        return 2;
      default:
        return 0;
    }
  }

  /// 为指定标签页加载数据 / Load data for specified tab
  void _loadDataForTab(TeamManagementTab tab, TeamManagementCubit cubit) {
    cubit.resetTeamData();
    cubit.updatePageNo(1);
    switch (tab) {
      case TeamManagementTab.member:
        cubit.fetchTeamMemberInfo();
        break;
      case TeamManagementTab.bet:
        cubit.fetchMyTeamDetail(type: TeamType.bet);
        break;
      case TeamManagementTab.profit:
        cubit.fetchMyTeamDetail(type: TeamType.recharge);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('team_management'.tr()),
        centerTitle: true,
        leading: InkWell(
          onTap: () {
            sl<NavigatorService>().unFocus();
            sl<NavigatorService>().pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 14.gw),
            child: Image(
              image: const AssetImage(Assets.iconBack),
              height: 20.gw,
              width: 20.gw,
            ),
          ),
        ),
        actions: [
          BlocSelector<TeamManagementCubit, TeamManagementState, TeamManagementTab?>(
            selector: (state) => state.tab,
            builder: (context, tab) {
              return _buildTabTypeDropdown(tab ?? TeamManagementTab.member);
            },
          ),
          SizedBox(width: 16.gw),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.gw),
        child: BlocListener<TeamManagementCubit, TeamManagementState>(
          listenWhen: (previous, current) => previous.tab != current.tab,
          listener: (context, state) {
            final cubit = context.read<TeamManagementCubit>();
            // 当状态改变时同步 TabController，但避免在 build 中直接设置
            final targetIndex = _indexFromTab(state.tab);
            if (_tabController.index != targetIndex) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted && _tabController.index != targetIndex) {
                  _tabController.animateTo(targetIndex);
                }
              });
            }
            // 确保数据加载 - 只有从 PopupMenuButton 触发的切换才需要这里加载数据
            // TabController 监听器已经处理了手动滑动的情况
            if (state.tab != null) {
              _loadDataForTab(state.tab!, cubit);
            }
          },
          child: TabBarView(
            controller: _tabController,
            children: [
              TeamMember(),
              TeamDetailsBet(),
              TeamDetailsRecharge(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabTypeDropdown(TeamManagementTab tab) {
    return PopupMenuButton<TeamManagementTab>(
      onSelected: (TeamManagementTab selectedTab) {
        context.read<TeamManagementCubit>().updateTab(selectedTab);
        // TabController 同步由 BlocListener 处理，避免重复调用
      },
      offset: const Offset(0, 35),
      color: context.colorTheme.foregroundColor,
      // 明确设置背景色 / Explicitly set background color
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.gw),
        side: BorderSide(
          color: context.colorTheme.borderB, // 添加边框以增强对比度 / Add border for better contrast
          width: 1.0,
        ),
      ),
      itemBuilder: (BuildContext context) =>
          TeamManagementTab.values.map((tabItem) => _buildPopupMenuItem(context, tabItem, tab)).toList(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 8.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(10.gw),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              tab.label, // 直接使用枚举的 label 属性 / Directly use enum's label property
              style: context.textTheme.title.fs13.w500,
            ),
            SizedBox(width: 4.gw),
            Icon(
              Icons.arrow_drop_down,
              size: 20.gw,
              color: context.colorTheme.textTitle,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建弹出菜单项 / Build popup menu item
  PopupMenuItem<TeamManagementTab> _buildPopupMenuItem(
    BuildContext context,
    TeamManagementTab tabItem,
    TeamManagementTab currentTab,
  ) {
    final isSelected = tabItem == currentTab;
    return PopupMenuItem<TeamManagementTab>(
      value: tabItem,
      child: Center(
        child: Text(
          tabItem.label, // 直接使用枚举的 label 属性 / Directly use enum's label property
          style: isSelected ? context.textTheme.primary.w500 : context.textTheme.title.w500,
        ),
      ),
    );
  }
}

class TeamMember extends StatelessWidget {
  TeamMember({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchTeamMemberInfo();
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreTeamMemberInfo();
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using CommonTable with level badges
            if (state.teamMembersNetState == NetState.empty)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamMembersNetState == NetState.success)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamMembersEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 3,
                            ),
                            CommonTableColumn(
                              title: 'levels'.tr(),
                              key: 'level',
                              flex: 2,
                              style: CommonTableColumnStyle.levelBadge, // Badge style for levels
                            ),
                            CommonTableColumn(
                              title: 'join_time'.tr(),
                              key: 'joinTime',
                              flex: 4,
                            ),
                          ],
                          data: _getTeamMembersTableData(state.teamMembersEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  /// Converts team members data to table format
  List<List<String>> _getTeamMembersTableData(List<TeamMembersRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              _getLevelText(record.level ?? 0), // This will be styled as level badge
              record.registerDate == null
                  ? '-'
                  : TimeUtil.convertTimeStampToTime(record.registerDate!), // Join Time (HH:mm:ss)
            ])
        .toList();
  }

  /// Helper method to get level text
  String _getLevelText(int level) {
    switch (level) {
      case 1:
        return '直属';
      case 2:
        return '2级';
      case 3:
        return '3级';
      case 4:
        return '4级';
      case 5:
        return '5级';
      default:
        return '直属';
    }
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () =>
                  context.read<TeamManagementCubit>().fetchTeamMemberInfo(childUserId: _controller.text.trim()),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TeamDetailsBet extends StatelessWidget {
  TeamDetailsBet({super.key});

  final TextEditingController _controller = TextEditingController();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.bet);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.bet);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using basic CommonTable for Team bet
            if (state.teamDetailsNetState == NetState.empty)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.success)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 3,
                            ),
                            CommonTableColumn(
                              title: 'bet_amount'.tr(),
                              key: 'betAmount',
                              flex: 4,
                            ),
                            CommonTableColumn(
                              title: 'contribution_commission'.tr(),
                              key: 'commission',
                              flex: 2,
                            ),
                          ],
                          data: _getTeamBetTableData(state.teamDetailsEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  /// Converts team bet data to table format
  List<List<String>> _getTeamBetTableData(List<TeamDetailsRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              record.amount?.toString() ?? '0',
              record.commissionAmount?.toString() ?? '0',
            ])
        .toList();
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text.trim(), type: TeamType.bet),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class TeamDetailsRecharge extends StatelessWidget {
  TeamDetailsRecharge({super.key});

  final TextEditingController _controller = TextEditingController();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh(BuildContext context) {
    context.read<TeamManagementCubit>().updatePageNo(1);
    context.read<TeamManagementCubit>().fetchMyTeamDetail(type: TeamType.recharge);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  Future<void> _onLoading(BuildContext context) async {
    final hasMore = await context.read<TeamManagementCubit>().loadMoreMyTeamDetail(type: TeamType.recharge);
    if (hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TeamManagementCubit, TeamManagementState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 10.gw),
            _buildSearchField(context),
            SizedBox(height: 14.gw),
            // Using basic CommonTable for Team Profit
            if (state.teamDetailsNetState == NetState.empty)
              Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 50.gw),
                  child: Text('no_data_found'.tr()),
                ),
              ),
            if (state.teamDetailsNetState == NetState.success)
              Expanded(
                child: AnimationLimiter(
                  child: CommonRefresher(
                    onRefresh: () => _onRefresh(context),
                    onLoading: () => _onLoading(context),
                    refreshController: _refreshController,
                    enablePullDown: true,
                    enablePullUp: true,
                    listWidget: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.gw),
                        child: CommonTable(
                          columns: [
                            CommonTableColumn(
                              title: '${'subordinate_count'.tr()}(${state.teamDetailsEntity?.total ?? 0})',
                              key: 'agent',
                              flex: 5,
                            ),
                            CommonTableColumn(
                              title: 'withdrawal_amount'.tr(),
                              key: 'withdrawAmount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'recharge_amount'.tr(),
                              key: 'amount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'profit_loss_amount'.tr(),
                              key: 'profitAmount',
                              flex: 6,
                            ),
                            CommonTableColumn(
                              title: 'contribution_commission'.tr(),
                              key: 'commissionAmount',
                              flex: 5,
                            ),
                          ],
                          data: _getTeamProfitTableData(state.teamDetailsEntity?.records ?? []),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Converts team profit data to table format
  List<List<String>> _getTeamProfitTableData(List<TeamDetailsRecords> records) {
    return records
        .map((record) => [
              record.subUserNo?.maskString() ?? '',
              record.withdrawAmount?.toString() ?? '0',
              record.amount?.toString() ?? '0',
              record.profitAmount?.toString() ?? '0',
              record.commissionAmount?.toString() ?? '0',
            ])
        .toList();
  }

  Widget _buildSearchField(BuildContext context) {
    return Container(
      height: 42.gw,
      width: 400.gw,
      padding: EdgeInsets.all(4.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(28.gw),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SizedBox(width: 8.gw),
                Icon(
                  Icons.search,
                  size: 24.gw,
                  color: context.colorTheme.textHighlight,
                ),
                SizedBox(width: 8.gw),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    style: context.textTheme.highlight,
                    decoration: InputDecoration(
                      fillColor: context.theme.cardColor,
                      hintText: 'enter_agent_id'.tr(),
                      hintStyle: context.textTheme.highlight,
                      filled: true,
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(28.gw),
                        borderSide: BorderSide.none,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 34.gw,
            padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 6.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.btnBgPrimary,
              borderRadius: BorderRadius.circular(24.gw),
              border: Border.all(
                color: context.colorTheme.btnBorderPrimary,
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () => context
                  .read<TeamManagementCubit>()
                  .fetchMyTeamDetail(childUserId: _controller.text.trim(), type: TeamType.recharge),
              child: Text(
                'search'.tr(),
                style: context.textTheme.btnPrimary.fs13.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
