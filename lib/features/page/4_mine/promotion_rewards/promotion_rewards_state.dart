// lib/features/page/4_mine/promotion_rewards/promotion_rewards_state.dart

import 'package:equatable/equatable.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/models/entities/commission_overview_entity.dart';
import '../../../../core/models/entities/my_team_entity.dart';
import '../../../../core/models/entities/team_entity.dart';

class PromotionRewardsState extends Equatable {
  final int currentTabIndex; // promotion main screen tab
  final int currentTeamLevelIndex; // team pageview
  final String? error;
  final TeamEntity? teamEntity;
  final NetState teamNetState;
  final MyTeamEntity? myTeamEntity;
  final NetState myTeamNetState;
  final CommissionOverviewEntity? commissionOverviewEntity;
  final NetState commissionReceiveState;
  final String? userInviteLink;
  final NetState userInviteLinkNetState;
  final NetState commissionOverviewNetState;
  final String? rulePicture;

  const PromotionRewardsState({
    this.currentTabIndex = 0,
    this.currentTeamLevelIndex = 0,
    this.error,
    this.teamEntity,
    this.teamNetState = NetState.idle,
    this.myTeamEntity,
    this.myTeamNetState = NetState.idle,
    this.commissionOverviewEntity,
    this.commissionOverviewNetState = NetState.idle,
    this.commissionReceiveState = NetState.idle,
    this.userInviteLink,
    this.userInviteLinkNetState = NetState.idle,
    this.rulePicture,
  });

  PromotionRewardsState copyWith({
    String? error,
    TeamEntity? teamEntity,
    NetState? teamNetState,
    MyTeamEntity? myTeamEntity,
    NetState? myTeamNetState,
    int? currentTabIndex,
    int? currentTeamLevelIndex,
    CommissionOverviewEntity? commissionOverviewEntity,
    NetState? commissionOverviewNetState,
    NetState? commissionReceiveState,
    String? userInviteLink,
    NetState? userInviteLinkNetState,
    String? rulePicture,
  }) {
    return PromotionRewardsState(
      error: error ?? this.error,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      currentTeamLevelIndex: currentTeamLevelIndex ?? this.currentTeamLevelIndex,
      teamEntity: teamEntity ?? this.teamEntity,
      teamNetState: teamNetState ?? this.teamNetState,
      myTeamEntity: myTeamEntity ?? this.myTeamEntity,
      myTeamNetState: myTeamNetState ?? this.myTeamNetState,
      commissionOverviewEntity: commissionOverviewEntity ?? this.commissionOverviewEntity,
      commissionOverviewNetState: commissionOverviewNetState ?? this.commissionOverviewNetState,
      commissionReceiveState: commissionReceiveState ?? this.commissionReceiveState,
      userInviteLink: userInviteLink ?? this.userInviteLink,
      userInviteLinkNetState: userInviteLinkNetState ?? this.userInviteLinkNetState,
      rulePicture: rulePicture ?? this.rulePicture,
    );
  }

  @override
  List<Object?> get props => [
        error,
        currentTabIndex,
        currentTeamLevelIndex,
        teamEntity,
        myTeamEntity,
        teamNetState,
        myTeamNetState,
        commissionOverviewEntity,
        commissionOverviewNetState,
        commissionReceiveState,
        userInviteLink,
        userInviteLinkNetState,
        rulePicture,
      ];
}
