part of 'commission_records_cubit.dart';

class CommissionRecordsState extends Equatable {
  final TeamType paymentTabIndex;
  final DayType dateType;
  final CommissionDetailsEntity? commissionDetailsEntity;
  final NetState commissionDetailsNetState;
  final int pageNo;
  final bool isNoMoreData;
  final String? error;

  const CommissionRecordsState(
      {this.paymentTabIndex = TeamType.bet,
      this.dateType = DayType.yesterday,
      this.commissionDetailsEntity,
      this.commissionDetailsNetState = NetState.idle,
      this.pageNo = 1,
      this.isNoMoreData = false,
      this.error});

  @override
  List<Object?> get props =>
      [paymentTabIndex, dateType, commissionDetailsEntity, commissionDetailsNetState, pageNo, isNoMoreData, error,];

  CommissionRecordsState copyWith(
      {TeamType? paymentTabIndex,
      DayType? dateType,
      CommissionDetailsEntity? commissionDetailsEntity,
      NetState? commissionDetailsNetState,
      int? pageNo,
      bool? isNoMoreData,
      bool resetCommissionDetailsEntity = false,
      String? error}) {
    return CommissionRecordsState(
        paymentTabIndex: paymentTabIndex ?? this.paymentTabIndex,
        dateType: dateType ?? this.dateType,
        commissionDetailsEntity: resetCommissionDetailsEntity ? null : commissionDetailsEntity ?? this.commissionDetailsEntity,
        commissionDetailsNetState: commissionDetailsNetState ?? this.commissionDetailsNetState,
        pageNo: pageNo ?? this.pageNo,
        isNoMoreData: isNoMoreData ?? this.isNoMoreData,
        error: error ?? this.error);
  }
}
