import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/agent.dart';

import 'agent_recruitment_state.dart';

class AgentRecruitmentCubit extends Cubit<AgentRecruitmentState> {
  AgentRecruitmentCubit() : super(const AgentRecruitmentState()) {
    fetchCustomerServiceConfig();
  }

  Future<void> fetchCustomerServiceConfig() async {
    emit(state.copyWith(netState: NetState.loading));
    try {
      final res = await AgentApi.fetchCustomerServiceConfig();
      emit(state.copyWith(
        data: res?.list,
        netState: NetState.success,
      ));
    } on Exception catch (_) {
      emit(state.copyWith(
        netState: NetState.failed,
      ));
    }
  }
}
