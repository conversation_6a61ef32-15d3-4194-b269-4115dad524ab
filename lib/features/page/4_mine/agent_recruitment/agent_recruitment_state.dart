import 'package:equatable/equatable.dart';
import 'package:wd/core/models/entities/customer_service_config_entity.dart';

import '../../../../core/base/base_state.dart';

class AgentRecruitmentState extends Equatable {
  final List<CustomerServiceConfigData>? data;
  final NetState netState;

  const AgentRecruitmentState({
    this.data,
    this.netState = NetState.idle,
  });

  AgentRecruitmentState copyWith({
    List<CustomerServiceConfigData>? data,
    NetState? netState,
  }) {
    return AgentRecruitmentState(
      data: data ?? this.data,
      netState: netState ?? this.netState,
    );
  }

  @override
  List<Object?> get props => [
        data,
        netState,
      ];
}
