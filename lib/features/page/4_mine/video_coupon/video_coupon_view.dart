import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/models/apis/time_fetcher.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/4_mine/video_coupon/video_coupon_state.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_textfield.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'video_coupon_cubit.dart';

class VideoCouponPage extends BasePage {
  const VideoCouponPage({super.key});

  @override
  BasePageState<BasePage> getState() => _VideoCouponPageState();
}

class _VideoCouponPageState extends BasePageState {
  final textController = TextEditingController();

  @override
  void initState() {
    pageTitle = "redeem_code".tr();
    super.initState();
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = BlocProvider.of<VideoCouponCubit>(context);
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.gw),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 12.gw),
                    _buildCouponCard(),
                    SizedBox(height: 12.gw),
                    CommonCard(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "redeem_code_hint".tr(),
                            style: context.textTheme.btnSecondary.fs16,
                          ),
                          SizedBox(height: 12.gw),
                          _buildInputField(),
                          SizedBox(height: 12.gw),
                          CommonButton(
                            title: "redeemNow".tr(),
                            onPressed: () {
                              sl<NavigatorService>().unFocus();
                              cubit.userOnClickSubmit(textController);
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.gw),
                    BlocBuilder<VideoCouponCubit, VideoCouponState>(
                      builder: (context, state) {
                        return HeaderContentCard(
                            margin: EdgeInsets.zero,
                          header: Text(
                            "importantRules".tr(),
                            style: context.textTheme.primary.fs16,
                          ),
                          content: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [

                                if (state.rulePictureState == NetState.failed)
                                  NetErrorWidget(refreshMethod: () => cubit.fetchRulePicture()),

                                if (state.rulePicture != null)
                                AppImage(
                                  imageUrl: state.rulePicture!,
                                  fit: BoxFit.fill,
                                ),
                              ],
                            ),
                          )
                        );
                      },
                    ),
                    SizedBox(height: 12.gw),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCouponCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(20.gw, 20.gw, 30.gw, 20.gw),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage("assets/images/mine/video_coupon/bg.png"),
          fit: BoxFit.fill,
        ),
      ),
      child: BlocSelector<UserCubit, UserState, ({String? dayStr, String? expiredDateStr})>(
        selector: (state) => (
          dayStr: state.videoVipInfo?.days.toString(),
          expiredDateStr: state.videoVipInfo?.expiredDate,
        ),
        builder: (context, model) {
          return Stack(
            clipBehavior: Clip.none,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    'remain_days'.tr(),
                    style: TextStyle(fontSize: 14.fs, color: Colors.white),
                  ),
                  SizedBox(height: 4.gw),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: model.dayStr ?? '0',
                          style:
                              context.textTheme.btnPrimary.fs30.w700.copyWith(color: context.colorTheme.btnBgPrimary),
                        ),
                        WidgetSpan(
                          alignment: PlaceholderAlignment.bottom,
                          child: Text(
                            ' ${'days'.tr()}',
                            style: context.textTheme.btnPrimary.w700.copyWith(color: context.colorTheme.btnBgPrimary),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 14.gw),
                  if (model.dayStr != null && (int.tryParse(model.dayStr!) ?? 0) > 0)
                    Row(
                      children: [
                        Icon(Icons.timer_outlined, color: context.colorTheme.textSecondary),
                        SizedBox(width: 8.gw),
                        Text(
                          '${'expiry'.tr()}: ${TimeFetcher.formatDateToDDMMYYYY(model.expiredDateStr!)}',
                          style: context.textTheme.btnPrimary.w700.copyWith(color: context.colorTheme.textSecondary),
                        ),
                      ],
                    ),
                ],
              ),
              Positioned(
                right: -20,
                bottom: -10,
                child: Image.asset("assets/images/mine/video_coupon/coins.png", width: 247.gw, height: 90.gw),
              ),
              Positioned(
                right: -20,
                top: -10,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 6.gw),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4.gw),
                        color: const Color(0xff090808).withOpacity(0.48), // black glass effect
                      ),
                      child: Center(
                        child: AneText('unlimited_viewing'.tr(), style: context.textTheme.primary.fs12),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildInputField() {
    return CommonTextField(
      controller: textController,
      isUnderline: true,
      hintText: 'enter_redeem_code'.tr(),
    );
  }
}
