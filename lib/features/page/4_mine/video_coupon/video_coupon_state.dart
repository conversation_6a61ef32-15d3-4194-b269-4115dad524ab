import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';

class VideoCouponState extends Equatable {
  final NetState rulePictureState;
  final String? rulePicture;

  const VideoCouponState({
    this.rulePictureState = NetState.idle,
    this.rulePicture,
  });

  VideoCouponState copyWith({
    NetState? rulePictureState,
    String? rulePicture,
  }) {
    return VideoCouponState(
      rulePictureState: rulePictureState ?? this.rulePictureState,
      rulePicture: rulePicture ?? this.rulePicture,
    );
  }

  @override
  List<Object?> get props => [
        rulePictureState,
        rulePicture,
      ];
}
