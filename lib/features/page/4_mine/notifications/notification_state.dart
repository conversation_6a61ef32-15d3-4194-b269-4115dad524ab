import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/notifications_response_entity.dart';

import 'package:equatable/equatable.dart';

class NotificationState extends BaseState<NotificationsRecords> with EquatableMixin {
  final int pageNo;
  final int total;
  final int unreadCount;

  const NotificationState({
    // BaseState 用到的
    super.netState = NetState.idle,
    super.isNoMoreDataState = false,
    super.dataList = const [],

    // 自身
    this.pageNo = 1,
    this.total = 0,
    this.unreadCount = 0,
  });

  NotificationState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    List<NotificationsRecords>? dataList,
    int? pageNo,
    int? total,
    int? unreadCount,
  }) {
    return NotificationState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      pageNo: pageNo ?? this.pageNo,
      total: total ?? this.total,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }

  @override
  List<Object?> get props => [
    netState,
    isNoMoreDataState,
    dataList,
    pageNo,
    total,
    unreadCount,
  ];
}

