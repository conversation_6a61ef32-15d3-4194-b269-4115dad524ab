import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/notification.dart';
import 'package:wd/core/models/entities/notifications_response_entity.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import 'notification_state.dart';

class NotificationsCubit extends Cubit<NotificationState> {
  NotificationsCubit() : super(const NotificationState()) {
    _startRefreshTimer();
  }

  Timer? _refreshTimer;

  /// 设置1分钟轮询
  void _startRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _refreshFirstPage();
    });
  }

  Future<void> _refreshFirstPage() async {
    final currentPage = state.pageNo;
    final result = await NotificationApi.fetchAllNotificationList(1);

    if (result.errorString != null) return;

    // 当前列表中的 ID 集合
    final existingIds = state.dataList?.map((e) => e.id).toSet() ?? {};

    // 过滤出新的通知（相对现有列表去重）
    final newNotifications = result.notifications
        .where((n) => !existingIds.contains(n.id))
        .toList(growable: false);

    if (newNotifications.isEmpty) return;

    // 新通知插入到列表开头
    final merged = <NotificationsRecords>[
      ...newNotifications,
      ...?state.dataList,
    ];

    // 仅在当前就是第一页时，才更新呈现状态
    final nextNetState = (currentPage == 1)
        ? (merged.isEmpty ? NetState.empty : NetState.success)
        : state.netState;

    emit(state.copyWith(
      dataList: merged,
      total: result.total,
      netState: nextNetState,
    ));
  }


  @override
  Future<void> close() {
    _refreshTimer?.cancel();
    return super.close();
  }

  Future<void> fetchNotifications({bool updateCount = false, bool reset = false}) async {
    // 重置分页与数据
    if (reset) {
      emit(state.copyWith(
        dataList: const <NotificationsRecords>[],
        netState: NetState.idle,
        isNoMoreDataState: false,
        pageNo: 1,
        total: 0,
      ));
    }

    // 若只更新未读计数，同时控制loading状态
    if (updateCount) {
      if (state.netState == NetState.loading) return;
      emit(state.copyWith(netState: NetState.loading));
      // 并行或串行都可，这里串行以确保状态有序
      await fetchUnreadCount();
    }

    // 拉取列表
    final result = await NotificationApi.fetchAllNotificationList(state.pageNo);

    if (result.errorString == null) {
      final merged = <NotificationsRecords>[
        ...?state.dataList,
        ...result.notifications,
      ];

      final noMore = result.total <= merged.length;

      emit(state.copyWith(
        dataList: merged,
        total: result.total,
        netState: merged.isEmpty ? NetState.empty : NetState.success,
        isNoMoreDataState: noMore,
      ));
    } else {
      emit(state.copyWith(netState: NetState.showReload));
    }
  }

  Future<void> fetchUnreadCount() async {
    final result = await NotificationApi.getUnreadCount();
    if (result != null) {
      emit(state.copyWith(unreadCount: result));
    }
  }

  Future<void> markAsRead(int id) async {
    final ok = await NotificationApi.updateNotificationReadStatus(id);
    if (ok ?? false) {
      // 更新未读计数
      await fetchUnreadCount();

      // 就地生成新列表（不可变）
      final updated = state.dataList
          ?.map((n) => n.id == id ? n.copyWith(siteMessageRead: 1) : n)
          .toList(growable: false);

      emit(state.copyWith(dataList: updated));
    }
  }

  Future<void> markAllAsRead() async {
    GSEasyLoading.showLoading();
    final ok = await NotificationApi.updateAllNotificationReadStatus();
    if (ok ?? false) {
      // 直接复用已有流程：重置并刷新 + 更新未读
      await fetchNotifications(updateCount: true, reset: true);
    }
    GSEasyLoading.dismiss();
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void updateIsNoMoreDataState(bool value) {
    emit(state.copyWith(isNoMoreDataState: value));
  }

  void resetState() {
    emit(state.copyWith(
      dataList: const <NotificationsRecords>[],
      unreadCount: 0,
      netState: NetState.idle,
      pageNo: 1,
      total: 0,
      isNoMoreDataState: false,
    ));
  }
}
