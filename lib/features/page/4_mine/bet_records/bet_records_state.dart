import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/bet_record_entity.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';

import 'package:equatable/equatable.dart';

class BetRecordListState extends BaseState<BetRecordPageRecords> with EquatableMixin {
  final int pageNo;
  final DateTime startDate;
  final DateTime endDate;
  final String selectedLotteryType;
  final String selectedStatus;
  final int betAmountToday;
  final int totalWinToday;
  final int? totalSendAmount;
  final List<Lottery> lotteryList;

  // 非 const（因为要用 DateTime.now()）
  BetRecordListState({
    super.netState = NetState.loading,     // 只保留用到的 BaseState 字段
    super.isNoMoreDataState = false,
    super.dataList = const [],
    this.pageNo = 1,
    DateTime? startDate,
    DateTime? endDate,
    this.selectedLotteryType = '全部',
    this.selectedStatus = '全部',
    this.betAmountToday = 0,
    this.totalWinToday = 0,
    this.totalSendAmount,
    this.lotteryList = const [],
  })  : startDate = startDate ?? DateTime.now(),
        endDate = endDate ?? DateTime.now();

  BetRecordListState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<BetRecordPageRecords>? dataList,
    int? pageNo,
    DateTime? startDate,
    DateTime? endDate,
    String? selectedLotteryType,
    String? selectedStatus,
    int? betAmountToday,
    int? totalWinToday,
    int? totalSendAmount,       // 允许显式传 null：用 `setTotalSendAmountNull` 需求可再扩展
    List<Lottery>? lotteryList,
  }) {
    return BetRecordListState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      pageNo: pageNo ?? this.pageNo,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      selectedLotteryType: selectedLotteryType ?? this.selectedLotteryType,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      betAmountToday: betAmountToday ?? this.betAmountToday,
      totalWinToday: totalWinToday ?? this.totalWinToday,
      totalSendAmount: totalSendAmount ?? this.totalSendAmount,
      lotteryList: lotteryList ?? this.lotteryList,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    isNoMoreDataState,
    dataList,
    // 自身字段
    pageNo,
    startDate,
    endDate,
    selectedLotteryType,
    selectedStatus,
    betAmountToday,
    totalWinToday,
    totalSendAmount,
    lotteryList,
  ];
}

