import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/models/entities/bet_record_entity.dart';

import 'package:wd/features/page/4_mine/bet_records/bet_records_state.dart';
import 'package:wd/shared/widgets/app_bar/lottery_detail_appbar.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:intl/intl.dart';

import 'bet_records_cubit.dart';

class BetRecordListPage extends BasePage {
  const BetRecordListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _BetRecordListPageState();
}

class _BetRecordListPageState extends BasePageState<BetRecordListPage> with SingleTickerProviderStateMixin {
  late RefreshController _refreshController;
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    pageTitle = "投注记录";
    isNeedEmptyDataWidget = false;
    isRenderHeader = false;
    _refreshController = RefreshController(initialRefresh: false);
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animation = Tween<double>(begin: 1, end: 0).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      _animationController.forward();
    } else if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      _animationController.reverse();
    }
  }

  void _onRefresh() {
    context.read<BetRecordListCubit>().updatePageNo(1);
    context.read<BetRecordListCubit>().updateIsNoMoreDataState(false);
    context.read<BetRecordListCubit>().fetchListData();
  }

  void _onLoading() {
    context.read<BetRecordListCubit>().updatePageNoToNext();
    context.read<BetRecordListCubit>().fetchListData();
  }

  Widget _buildFilterBar() {
    return BlocBuilder<BetRecordListCubit, BetRecordListState>(
      builder: (ctx, state) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 8.gw),
          child: Wrap(
            spacing: 8.gw,
            runSpacing: 2.gw,
            alignment: WrapAlignment.start,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              ActionChip(
                label: Text(
                  DateFormat('yyyy-MM-dd').format(state.startDate),
                  style: TextStyle(color: Colors.black, fontSize: 14.fs),
                ),
                onPressed: () => _showDatePicker(context, true),
                backgroundColor: Colors.white,
                side: const BorderSide(color: Colors.grey),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
              ),
              ActionChip(
                label: Text(
                  DateFormat('yyyy-MM-dd').format(state.endDate),
                  style: TextStyle(color: Colors.black, fontSize: 14.fs),
                ),
                onPressed: () => _showDatePicker(context, false),
                backgroundColor: Colors.white,
                side: const BorderSide(color: Colors.grey),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
              ),
              _buildLotteryTypeDropdown(
                state.selectedLotteryType,
                ['全部'] + state.lotteryList.map((l) => l.lotteryName).toList(),
                (newValue) {
                  if (newValue == '全部') {
                    context.read<BetRecordListCubit>().updateSelectedLotteryType('全部');
                  } else {
                    final selectedLottery = state.lotteryList.firstWhere(
                      (lottery) => lottery.lotteryName == newValue,
                    );
                    context.read<BetRecordListCubit>().updateSelectedLotteryType(selectedLottery.id.toString());
                  }
                },
              ),


              _buildStatusDropdown(
                state.selectedStatus,
                ['全部', '已中奖', '未中奖', '等待开奖'],
                (newValue) {
                  context.read<BetRecordListCubit>().updateSelectedStatus(newValue!);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDatePicker(BuildContext context, bool isStartDate) {
    DateTime tempDate = isStartDate
        ? context.read<BetRecordListCubit>().state.startDate
        : context.read<BetRecordListCubit>().state.endDate;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext builder) {
        return Container(
          height: MediaQuery.of(context).copyWith().size.height / 3,
          color: Colors.white,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    child: Text('取消', style: Theme.of(context).textTheme.titleLarge),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  Text(isStartDate ? "开始日期" : "结束日期", style: Theme.of(context).textTheme.titleLarge),
                  CupertinoButton(
                    child: const Text('确定'),
                    onPressed: () {
                      if (isStartDate) {
                        context.read<BetRecordListCubit>().updateStartDate(tempDate);
                      } else {
                        context.read<BetRecordListCubit>().updateEndDate(tempDate);
                      }
                      context.read<BetRecordListCubit>().fetchListData();
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
              Expanded(
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: tempDate,
                  minimumDate: DateTime.now().subtract(const Duration(days: 365)),
                  maximumDate: DateTime.now(),
                  onDateTimeChanged: (DateTime newDateTime) {
                    tempDate = newDateTime;
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusDropdown(String value, List<String> items, void Function(String?) onChanged) {
    return Container(
      height: 35.gw,
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: Colors.grey),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          dropdownColor: Colors.white,
          icon: Icon(Icons.arrow_drop_down, size: 18.gw),
          iconSize: 18.gw,
          isDense: true,
          style: TextStyle(fontSize: 12.fs, color: Colors.black),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildLotteryTypeDropdown(String value, List<String> items, void Function(String?) onChanged) {
    return Container(
      height: 35.gw,
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: Colors.grey),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          dropdownColor: Colors.white,
          icon: Icon(Icons.arrow_drop_down, size: 18.gw),
          iconSize: 18.gw,
          isDense: true,
          style: TextStyle(fontSize: 12.fs, color: Colors.black),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget mainPageWidget(BetRecordListState state) {
    return Column(
      children: [
        SizeTransition(
          sizeFactor: _animation,
          child: Column(
            children: [
              _buildFilterBar(),
              // 接口没有统计这两个数据，先暂时注释
              // Padding(
              //   padding: EdgeInsets.symmetric(vertical: 8.gw, horizontal: 16.gw),
              //   child: Text(
              //     '有效投注：${state.betAmountToday} 损益：${state.totalWinToday}',
              //     style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              //   ),
              // ),
            ],
          ),
        ),
        Expanded(
          child: _buildListView(state),
        ),
      ],
    );
  }

  Widget _buildListView(BetRecordListState state) {
    if (state.netState == NetState.empty) {
      return Center(child: emptyWidget());
    }
    if (state.netState == NetState.success) {
      return CommonRefresher(
        enablePullDown: true,
        enablePullUp: true,
        refreshController: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        listWidget: ListView.separated(
          controller: _scrollController,
          padding: EdgeInsets.only(top: 5.gw),
          itemBuilder: (context, index) {
            final model = state.dataList![index];
            return GestureDetector(
              onTap: () {
                // Navigate to detail page if needed
              },
              child: BetRecordCell(betRecord: model),
            );
          },
          separatorBuilder: (_, __) => SizedBox(height: 5.gw),
          itemCount: state.dataList!.length,
        ),
      );
    }
    return const SizedBox();
  }

  void _listener(BuildContext context, BetRecordListState state) {
    _refreshController.refreshCompleted();
    _refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      appBar: LotteryDetailAppBar(
        title: pageTitle,
      ),
      body: BlocConsumer<BetRecordListCubit, BetRecordListState>(
        listener: _listener,
        builder: (context, state) {
          return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
            context.read<BetRecordListCubit>().fetchListData();
          });
        },
      ),
    );
  }
}

class BetRecordCell extends StatelessWidget {
  final BetRecordPageRecords betRecord;

  const BetRecordCell({super.key, required this.betRecord});

  bool get isWaitingForDraw => betRecord.orderStatus == 10;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1.0),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${betRecord.lotteryName} (${betRecord.periodId}期)',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(color: context.theme.primaryColor),
              ),
              Text(
                isWaitingForDraw ? '等待开奖' : '已开奖',
                style: TextStyle(
                  fontSize: 14.0,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          Divider(indent: 0, endIndent: 1, thickness: 1, color: Theme.of(context).dividerColor),
          Text(
            '玩法：${betRecord.itemType}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 4.0),
          Text(
            '单号：${betRecord.betOrderNo}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 4.0),
          Text(
            '投注码：${betRecord.itemObject} ${betRecord.lotteryOptions.isNotEmpty ? "(${betRecord.lotteryOptions})" : ''}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 4.0),
          Text(
            '开奖码：${betRecord.openResult ?? '-'}',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                '投注金额：${betRecord.betAmount}，',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              // Text(
              //   '注数：1,',
              //   style: Theme.of(context).textTheme.titleMedium,
              // ),
              Text(
                '中奖金额：${betRecord.winAmount}',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              // Text(
              //   '中注数：0',
              //   style: Theme.of(context).textTheme.titleMedium,
              // ),
            ],
          ),
        ],
      ),
    );
  }
}
