import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

class TopUpHistoryListState extends BaseState<TopUpRecord> with EquatableMixin {
  final List<TransactFilterItem> filterWayList;
  final List<TransactFilterItem> filterTypeList;
  final RecordDateType currentDateType;
  final String userNo;
  final int pageNo;

  const TopUpHistoryListState({
    super.netState,
    super.isNoMoreDataState,
    super.isNetWorkFinish,
    super.dataList,
    super.netLoadCount,
    this.filterWayList = const [],
    this.filterTypeList = const [],
    this.currentDateType = RecordDateType.today,
    this.userNo = "",
    this.pageNo = 1,
  });

  /// copyWith 方法
  TopUpHistoryListState copyWith({
    NetState? netState,
    bool? isNoMoreDataState,
    bool? isNetWorkFinish,
    List<TopUpRecord>? dataList,
    int? netLoadCount,
    List<TransactFilterItem>? filterWayList,
    List<TransactFilterItem>? filterTypeList,
    RecordDateType? currentDateType,
    String? userNo,
    int? pageNo,
  }) {
    return TopUpHistoryListState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      isNetWorkFinish: isNetWorkFinish ?? this.isNetWorkFinish,
      dataList: dataList ?? this.dataList,
      netLoadCount: netLoadCount ?? this.netLoadCount,
      filterWayList: filterWayList ?? this.filterWayList,
      filterTypeList: filterTypeList ?? this.filterTypeList,
      currentDateType: currentDateType ?? this.currentDateType,
      userNo: userNo ?? this.userNo,
      pageNo: pageNo ?? this.pageNo,
    );
  }

  /// 等值判断（Equatable）
  @override
  List<Object?> get props => [
    netState,
    isNoMoreDataState,
    isNetWorkFinish,
    dataList,
    netLoadCount,
    filterWayList,
    filterTypeList,
    currentDateType,
    userNo,
    pageNo,
  ];
}