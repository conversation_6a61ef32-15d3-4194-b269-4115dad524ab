import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/top_up_record_entity.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'top_up_history_list_state.dart';

class TopUpHistoryListCubit extends Cubit<TopUpHistoryListState> {
  TopUpHistoryListCubit() : super(const TopUpHistoryListState()) {
    fetchListFilterWayList();
    fetchListFilterTypeList();
    fetchListData();
  }

  String lastWayCode = "";
  String lastTypeCode = "";

  void currentDateTypeChanged(RecordDateType type) {
    emit(state.copyWith(currentDateType: type));
    fetchListData();
  }

  void updatePageNo(int pageNo) {
    if (isClosed) return;
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    if (isClosed) return;
    emit(state.copyWith(pageNo: state.pageNo+1));
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    emit(state.copyWith(isNoMoreDataState: isNoMoreDataState));
  }

  fetchListFilterWayList() async {
    var list = await TransactApi.fetchTransactFilterWay(type: TransactType.withdraw);
    var tmpList = list.map((e) => TransactFilterItem.fromTransactFilterWay(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: 'all'.tr(), code: "", isSel: true);
      final list = List<TransactFilterItem>.from(tmpList)..insert(0, allType);
      emit(state.copyWith(filterWayList: list));
    }
  }

  fetchListFilterTypeList() async {
    var list = await TransactApi.fetchTransactFilterType(type: TransactType.withdraw);
    var tmpList = list.map((e) => TransactFilterItem.fromTransactFilterType(e)).toList();
    if (tmpList.isNotEmpty) {
      var allType = TransactFilterItem(name: 'all'.tr(), code: "", isSel: true);
      final list = List<TransactFilterItem>.from(tmpList)..insert(0, allType);
      emit(state.copyWith(filterTypeList: list));
    }
  }

  fetchListDataWithFilter() {
    if (isClosed) return;
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    if (wayCode != lastWayCode || typeCode != lastTypeCode) {
      lastWayCode = wayCode;
      lastTypeCode = typeCode;
      fetchListData();
    }
  }

  fetchListData() async {
    GSEasyLoading.showLoading();
    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final result = await TransactApi.fetchTopUpHistoryList(
      pageNo: state.pageNo,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      wayCode: wayCode,
      typeCode: typeCode,
    );
    GSEasyLoading.dismiss();
    if (result != null) {
      if (result.total <= state.dataList!.length) {
        updateIsNoMoreDataState(true);
      } else {
        updateIsNoMoreDataState(false);
      }

      List<TopUpRecord> dataList = [];
      if (state.pageNo == 1) {
        dataList = result.records;
      } else {
        dataList = List.from(dataList)..addAll(result.records);
      }
      emit(state.copyWith(
        netState: dataList.isEmpty ? NetState.empty : NetState.success,
        dataList: dataList,
      ));
    } else {
      emit(state.copyWith(netState: NetState.failed));
    }
  }
}
