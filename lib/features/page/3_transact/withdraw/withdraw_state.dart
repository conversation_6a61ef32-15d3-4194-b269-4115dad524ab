import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_user_bank_list_entity.dart';

import 'package:equatable/equatable.dart';

class WithdrawState extends BaseState<dynamic> with EquatableMixin {
  final double availableBalance;
  final String input;

  final bool fetchingBankInfo;
  final bool fetchingSubmit;

  /// 静态的提现方式列表（常量）
  final List<Map<String, String>> withdrawTypeList;

  final WithdrawType type;

  final WithdrawUserBankInfoEntity? currentSelBank;
  final WithdrawUserBankInfoEntity? currentSelWallet;
  final WithdrawUserBankInfoEntity? currentSelUsdt;

  final List<WithdrawUserBankBrief> myBankCardList;
  final List<WithdrawUserBankBrief> myWalletList;
  final List<WithdrawUserBankBrief> myUsdtList;

  final List<WithdrawManualChannelEntity> manualChannelList;
  final WithdrawManualChannelEntity? currentSelManualChannel;

  const WithdrawState({
    // 仅保留需要的 BaseState 字段
    super.netState = NetState.idle,

    // 自身字段默认值（等价于你原来的 init）
    this.availableBalance = 0,
    this.input = '',
    this.fetchingBankInfo = false,
    this.fetchingSubmit = false,
    this.withdrawTypeList = const [
      {"title": "bank_card", "image": "assets/images/transact/icon_payment_visa.png"},
      {"title": "e_wallet", "image": "assets/images/transact/icon_payment_wallet.png"},
      {"title": "manual_channel", "image": "assets/images/transact/icon_payment_manual_channel.png"},
    ],
    this.type = WithdrawType.bankCard,
    this.currentSelBank,
    this.currentSelWallet,
    this.currentSelUsdt,
    this.myBankCardList = const [],
    this.myWalletList = const [],
    this.myUsdtList = const [],
    this.manualChannelList = const [],
    this.currentSelManualChannel,
  });

  WithdrawState copyWith({
    // BaseState
    NetState? netState,

    // 自身字段
    double? availableBalance,
    String? input,
    bool? fetchingBankInfo,
    bool? fetchingSubmit,
    List<Map<String, String>>? withdrawTypeList,
    WithdrawType? type,
    WithdrawUserBankInfoEntity? currentSelBank,
    WithdrawUserBankInfoEntity? currentSelWallet,
    WithdrawUserBankInfoEntity? currentSelUsdt,
    List<WithdrawUserBankBrief>? myBankCardList,
    List<WithdrawUserBankBrief>? myWalletList,
    List<WithdrawUserBankBrief>? myUsdtList,
    List<WithdrawManualChannelEntity>? manualChannelList,
    WithdrawManualChannelEntity? currentSelManualChannel,
  }) {
    return WithdrawState(
      // BaseState
      netState: netState ?? this.netState,

      // 自身
      availableBalance: availableBalance ?? this.availableBalance,
      input: input ?? this.input,
      fetchingBankInfo: fetchingBankInfo ?? this.fetchingBankInfo,
      fetchingSubmit: fetchingSubmit ?? this.fetchingSubmit,
      withdrawTypeList: withdrawTypeList ?? this.withdrawTypeList,
      type: type ?? this.type,
      currentSelBank: currentSelBank ?? this.currentSelBank,
      currentSelWallet: currentSelWallet ?? this.currentSelWallet,
      currentSelUsdt: currentSelUsdt ?? this.currentSelUsdt,
      myBankCardList: myBankCardList ?? this.myBankCardList,
      myWalletList: myWalletList ?? this.myWalletList,
      myUsdtList: myUsdtList ?? this.myUsdtList,
      manualChannelList: manualChannelList ?? this.manualChannelList,
      currentSelManualChannel:
      currentSelManualChannel ?? this.currentSelManualChannel,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列你用到的）
    netState,

    // 自身字段
    availableBalance,
    input,
    fetchingBankInfo,
    fetchingSubmit,
    withdrawTypeList,
    type,
    currentSelBank,
    currentSelWallet,
    currentSelUsdt,
    myBankCardList,
    myWalletList,
    myUsdtList,
    manualChannelList,
    currentSelManualChannel,
  ];
}

