import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/core/utils/time_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'withdraw_history_list_state.dart';

class WithdrawHistoryListCubit extends Cubit<WithdrawHistoryListState> {
  WithdrawHistoryListCubit({RecordDateType? type}) : super(const WithdrawHistoryListState()) {
    if (type != null) {
      currentDateTypeChanged(type);
    } else {
      fetchListFilterWayList();
      fetchListFilterTypeList();
      fetchListData();
    }
  }

  String lastWayCode = "";
  String lastTypeCode = "";
  void currentDateTypeChanged(RecordDateType type) {
    if (state.currentDateType == type) return;
    emit(state.copyWith(
      currentDateType: type,
      pageNo: 1,
      isNoMoreDataState: false,
      // 如果切换筛选想立刻显示骨架屏，可放开：
      // dataList: const [],
      // netState: NetState.loading,
    ));
    fetchListData();
  }

  void updatePageNo(int pageNo) {
    emit(state.copyWith(pageNo: pageNo));
  }

  void updatePageNoToNext() {
    emit(state.copyWith(pageNo: state.pageNo + 1));
  }

  void updateIsNoMoreDataState(bool value) {
    emit(state.copyWith(isNoMoreDataState: value));
  }

  Future<void> fetchListFilterWayList() async {
    final list = await TransactApi.fetchTransactFilterWay(type: TransactType.withdraw);
    final tmpList = list
        .map((e) => TransactFilterItem.fromTransactFilterWay(e))
        .toList(growable: false);

    if (tmpList.isNotEmpty) {
      final allType = TransactFilterItem(name: 'all'.tr(), code: "", isSel: true);
      final merged = <TransactFilterItem>[allType, ...tmpList];
      emit(state.copyWith(filterWayList: merged));
    }
  }

  Future<void> fetchListFilterTypeList() async {
    final list = await TransactApi.fetchTransactFilterType(type: TransactType.withdraw);
    final tmpList = list
        .map((e) => TransactFilterItem.fromTransactFilterType(e))
        .toList(growable: false);

    if (tmpList.isNotEmpty) {
      final allType = TransactFilterItem(name: 'all'.tr(), code: "", isSel: true);
      final merged = <TransactFilterItem>[allType, ...tmpList];
      emit(state.copyWith(filterTypeList: merged));
    }
  }


  /// 过滤相同条件
  fetchListDataWithFilter() {
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).toList().join(",");
    if (wayCode != lastWayCode || typeCode != lastTypeCode) {
      lastWayCode = wayCode;
      lastTypeCode = typeCode;
      fetchListData();
    }
  }

  Future<void> fetchListData() async {
    // 可选：先发一帧 loading，UI 有感知
    emit(state.copyWith(netState: NetState.loading));

    GSEasyLoading.showLoading();

    final dateRange = TimeUtil.getDateRange(state.currentDateType);
    final wayCode = state.filterWayList.where((e) => e.isSel).map((e) => e.code).join(",");
    final typeCode = state.filterTypeList.where((e) => e.isSel).map((e) => e.code).join(",");

    final result = await TransactApi.fetchWithdrawHistoryList(
      pageNo: state.pageNo,
      startDate: dateRange.$1,
      endDate: dateRange.$2,
      wayCode: wayCode,
      typeCode: typeCode,
    );

    GSEasyLoading.dismiss();

    if (result == null) {
      emit(state.copyWith(
        netState: NetState.failed, // 或 NetState.showReload，按你项目习惯
      ));
      return;
    }

    // 合并列表（第一页覆盖，其他页追加）
    final merged = state.pageNo == 1
        ? result.records
        : [...?state.dataList, ...result.records];

    final noMore = result.total <= merged.length;

    emit(state.copyWith(
      dataList: merged,
      isNoMoreDataState: noMore,
      netState: merged.isEmpty ? NetState.empty : NetState.success,
    ));
  }


  Future<bool> operateWithdrawRecordRead(WithdrawRecord model) async {
    final flag = await TransactApi.operateWithdrawRecordRead(model.transactionNo);
    return flag;
  }
}
