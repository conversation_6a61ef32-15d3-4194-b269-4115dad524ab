import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/withdraw_record_entity.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';

import 'package:equatable/equatable.dart';

class WithdrawHistoryListState extends BaseState<WithdrawRecord> with EquatableMixin {
  final List<TransactFilterItem> filterWayList;
  final List<TransactFilterItem> filterTypeList;
  final RecordDateType currentDateType;
  final String userNo;
  final int pageNo;

  const WithdrawHistoryListState({
    // 仅保留需要的 BaseState 字段
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.dataList = const [],

    // 自身字段
    this.filterWayList = const [],
    this.filterTypeList = const [],
    this.currentDateType = RecordDateType.today,
    this.userNo = "",
    this.pageNo = 1,
  });

  WithdrawHistoryListState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<WithdrawRecord>? dataList,

    // 自身
    List<TransactFilterItem>? filterWayList,
    List<TransactFilterItem>? filterTypeList,
    RecordDateType? currentDateType,
    String? userNo,
    int? pageNo,
  }) {
    return WithdrawHistoryListState(
      // BaseState
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      // 自身
      filterWayList: filterWayList ?? this.filterWayList,
      filterTypeList: filterTypeList ?? this.filterTypeList,
      currentDateType: currentDateType ?? this.currentDateType,
      userNo: userNo ?? this.userNo,
      pageNo: pageNo ?? this.pageNo,
    );
  }

  @override
  List<Object?> get props => [
    // BaseState（只列用到的）
    netState,
    isNoMoreDataState,
    dataList,
    // 自身
    filterWayList,
    filterTypeList,
    currentDateType,
    userNo,
    pageNo,
  ];
}

