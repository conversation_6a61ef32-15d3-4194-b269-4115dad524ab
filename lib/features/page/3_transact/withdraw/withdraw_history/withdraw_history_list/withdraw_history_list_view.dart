import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/transact.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/transact/transact_history_filter_popup.dart';
import 'package:wd/shared/widgets/transact/withdraw/withdraw_history_cell.dart';

import '../../../../../../core/constants/assets.dart';
import 'withdraw_history_list_cubit.dart';
import 'withdraw_history_list_state.dart';

class WithdrawHistoryListPage extends BasePage {
  const WithdrawHistoryListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _WithdrawHistoryListPageState();
}

class _WithdrawHistoryListPageState extends BasePageState<WithdrawHistoryListPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = "withdraw_record".tr(); // 提现记录
    isNeedEmptyDataWidget = false;
    _tabController = TabController(length: 3, vsync: this)
      ..addListener(() {
        final type = RecordDateType.values[_tabController.index];
        context.read<WithdrawHistoryListCubit>().currentDateTypeChanged(type);
      });
    _getData();
  }

  _getData() {
    context.read<WithdrawHistoryListCubit>().fetchListData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  @override
  Widget right() {
    return GestureDetector(
        onTap: () {
          final cubit = context.read<WithdrawHistoryListCubit>();
          final state = cubit.state;
          TransactHistoryFilterPopup.show(
            context: context,
            type: TransactType.withdraw,
            filterWayList: state.filterWayList,
            filterTypeList: state.filterTypeList,
            onClickSure: () {
              cubit.updatePageNo(1);
              cubit.fetchListDataWithFilter();
            },
          );
        },
        child: Image.asset(Assets.iconFilter, width: 36.gw, height: 36.gw));
  }

  /// 下拉刷新
  void _onRefresh() {
    context.read<WithdrawHistoryListCubit>().updatePageNo(1);
    context.read<WithdrawHistoryListCubit>().updateIsNoMoreDataState(false);
    context.read<WithdrawHistoryListCubit>().fetchListData();
  }

  /// 下拉刷新
  void _onLoading() {
    context.read<WithdrawHistoryListCubit>().updatePageNoToNext();
    context.read<WithdrawHistoryListCubit>().fetchListData();
  }

  Widget mainPageWidget(WithdrawHistoryListState state) {
    return GSDateTabBarWidget(tabController: _tabController, children: [
      if (state.netState == NetState.empty) ...[
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              emptyWidget(),
            ],
          ),
        )
      ],
      if (state.netState == NetState.success) ...[
        Expanded(
          child: AnimationLimiter(
            child: CommonRefresher(
              enablePullDown: true,
              enablePullUp: true,
              refreshController: refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              listWidget: ListView.separated(
                padding: EdgeInsets.only(top: 10.gw),
                itemBuilder: (BuildContext context, int index) {
                  final model = state.dataList![index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      horizontalOffset: 50.0,
                      child: FadeInAnimation(
                        child: GestureDetector(
                          onTap: () {
                            sl<NavigatorService>().push(AppRouter.transactWithdrawHistoryDetail, arguments: model);
                          },
                          child: WithdrawHistoryCell(model: model),
                        ),
                      ),
                    ),
                  );
                },
                separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                itemCount: state.dataList!.length,
              ),
            ),
          ),
        ),
      ],
    ]);
  }

  void _listener(BuildContext context, WithdrawHistoryListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<WithdrawHistoryListCubit, WithdrawHistoryListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
