import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/net_error_widget.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/models/entities/activity_task_record_entity.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/theme/themes.dart';

import 'activity_records_list_cubit.dart';
import 'activity_records_list_state.dart';

/// 活动领取记录
class ActivityRecordsListPage extends BasePage {
  const ActivityRecordsListPage({super.key});

  @override
  BasePageState<BasePage> getState() => _ActivityRecordsListPageState();
}

class _ActivityRecordsListPageState
    extends BasePageState<ActivityRecordsListPage> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    pageTitle = 'act_reward_history'.tr();
    super.initState();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ActivityRecordsListCubit()..loadData(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<ActivityRecordsListCubit>(context);

    return BlocBuilder<ActivityRecordsListCubit, ActivityRecordsListState>(
      builder: (context, state) {
        return Column(
          children: [
            SizedBox(height: 14.gw),
            const _TableHeader(),
            if (state.netState == NetState.empty) ...[
              NetErrorWidget(
                  title: 'no_data'.tr(),
                  refreshMethod: () => cubit.fetchData()),
            ] else ...[
              Expanded(
                child: SmartRefresher(
                  controller: _refreshController,
                  header: const MaterialClassicHeader(),
                  enablePullUp: true,
                  onRefresh: () async {
                    await cubit.refresh();
                    _refreshController.resetNoData();
                    _refreshController.refreshCompleted();
                  },
                  onLoading: () async {
                    final hasMore = await cubit.loadMore();
                    if (hasMore) {
                      _refreshController.loadComplete();
                    } else {
                      _refreshController.loadNoData();
                    }
                  },
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 12.gw),
                    itemCount: state.dataList!.length,
                    itemBuilder: (context, index) {
                      final record =
                          state.dataList![index] as ActivityTaskRecord;
                      return Column(
                        children: [
                          _TableRow(
                            record: record,
                            index: index,
                            isFirst: index == 0,
                            isLast: index == state.dataList!.length - 1,
                          ),
                          Container(
                            height: 1,
                            color: const Color(0xFF000000),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ],
        );
      },
    );
  }
}

class _TableHeader extends StatelessWidget {
  const _TableHeader();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Container(
        height: 35.gw,
        decoration: ShapeDecoration(
          color: context.colorTheme.btnBgPrimary,
          shape: const RoundedRectangleBorder(
            side: BorderSide(
              width: 1,
              color: Color(0xFFFFE157),
            ),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
        ),
        child: _TableRowLayout(
          children: [
            _TableCell(
              text: 'act_event_name'.tr(),
              textStyle: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
              ),
              flex: 30,
              isBold: true,
              showRightBorder: true,
            ),
            _TableCell(
              text: 'act_event_type'.tr(),
              textStyle: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
              ),
              flex: 30,
              isBold: true,
              showRightBorder: true,
            ),
            _TableCell(
              text: 'time'.tr(),
              textStyle: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
              ),
              flex: 20,
              isBold: true,
              showRightBorder: true,
            ),
            _TableCell(
              text: 'act_event_quantity'.tr(),
              textStyle: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
              ),
              flex: 20,
              isBold: true,
            ),
          ],
        ),
      ),
    );
  }
}

class _TableRow extends StatelessWidget {
  final ActivityTaskRecord record;
  final int index;
  final bool isFirst;
  final bool isLast;

  const _TableRow({
    required this.record,
    required this.index,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 35.gw,
      decoration: BoxDecoration(
        color: const Color(0xFF202020),
        borderRadius: BorderRadius.vertical(
          bottom: isLast ? Radius.circular(8.gw) : Radius.zero,
        ),
      ),
      child: _TableRowLayout(
        children: [
          _TableCell(
            text: record.activeName,
            flex: 30,
            showRightBorder: true,
            textStyle: context.textTheme.title,
            enableAutoSize: true,
          ),
          _TableCell(
            text: _getActiveTypeText(record.activeType),
            flex: 30,
            showRightBorder: true,
            textStyle: context.textTheme.title,
          ),
          _TableCell(
            text: record.displayTime(),
            flex: 20,
            showRightBorder: true,
            textStyle: context.textTheme.title,
          ),
          _TableCell(
            text: '+${record.sendAmount}',
            flex: 20,
            textStyle: context.textTheme.primary,
            isBold: true,
          ),
        ],
      ),
    );
  }

  String _getActiveTypeText(int activeType) {
    switch (activeType) {
      case 1:
        return '游戏活动';
      case 2:
        return '自助领取';
      default:
        return '未知类型';
    }
  }
}

class _TableRowLayout extends StatelessWidget {
  final List<Widget> children;

  const _TableRowLayout({required this.children});

  @override
  Widget build(BuildContext context) {
    return Row(children: children);
  }
}

class _TableCell extends StatelessWidget {
  final String text;
  final int flex;
  final bool isBold;
  final TextStyle? textStyle;
  final bool showRightBorder;
  final bool enableAutoSize;
  const _TableCell({
    required this.text,
    required this.flex,
    this.isBold = false,
    this.textStyle,
    this.showRightBorder = false,
    this.enableAutoSize = false,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex,
      child: Container(
        decoration: showRightBorder
            ? const BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Color(0xFF000000),
                    width: 0.7,
                  ),
                ),
              )
            : null,
        child: Center(
          child: enableAutoSize
              ? AutoSizeText(
                  text.isEmpty ? "-----" : text,
                  textAlign: TextAlign.center,
                  style: textStyle?.copyWith(
                        fontWeight: isBold ? FontWeight.bold : null,
                      ) ??
                      TextStyle(
                        fontWeight: isBold ? FontWeight.bold : null,
                      ),
                  maxLines: 2,
                  minFontSize: 10,
                  maxFontSize: 14,
                )
              : Text(
                  text.isEmpty ? "-----" : text,
                  textAlign: TextAlign.center,
                  style: textStyle?.copyWith(
                        fontWeight: isBold ? FontWeight.bold : null,
                      ) ??
                      TextStyle(
                        fontWeight: isBold ? FontWeight.bold : null,
                      ),
                ),
        ),
      ),
    );
  }
}
