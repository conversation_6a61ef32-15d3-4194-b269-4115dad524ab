import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/activity.dart';

import 'activity_records_list_state.dart';

class ActivityRecordsListCubit extends Cubit<ActivityRecordsListState> {
  ActivityRecordsListCubit() : super(const ActivityRecordsListState());

  Future<void> loadData() async {
    if (isClosed) return;
    // 回到第一页并进入加载态（可按需去掉 dataList 清空）
    emit(state.copyWith(
      pageNo: 1,
      isNoMoreDataState: false,
      dataList: const [],
      netState: NetState.loading,
    ));
    await fetchData();
  }

  Future<void> refresh() async {
    if (isClosed) return;
    emit(state.copyWith(
      pageNo: 1,
      isNoMoreDataState: false,
      dataList: const [],
      netState: NetState.loading,
    ));
    await fetchData();
  }

  Future<bool> loadMore() async {
    if (isClosed) return false;
    if (state.isNoMoreDataState == true) return false;

    emit(state.copyWith(pageNo: state.pageNo + 1));
    await fetchData();
    return !(state.isNoMoreDataState ?? false);
  }

  Future<void> fetchData() async {
    if (isClosed) return;

    final result = await ActivityApi.fetchActivityTaskRecordList(pageNo: state.pageNo);

    if (result == null) {
      // 按项目习惯也可以用 NetState.showReload
      emit(state.copyWith(netState: NetState.failed));
      return;
    }

    // 合并数据：第一页覆盖，其它页追加
    final merged = state.pageNo == 1
        ? result.records
        : [...?state.dataList, ...result.records];

    final noMore = result.total <= merged.length;

    emit(state.copyWith(
      dataList: merged,
      isNoMoreDataState: noMore,
      netState: merged.isEmpty ? NetState.empty : NetState.success,
    ));
  }

}
