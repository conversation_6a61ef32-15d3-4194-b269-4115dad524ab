import 'package:equatable/equatable.dart';
import 'package:wd/core/base/base_state.dart';

import 'package:wd/core/models/entities/activity_task_record_entity.dart';

class ActivityRecordsListState extends BaseState<ActivityTaskRecord> with EquatableMixin {
  final int pageNo;

  const ActivityRecordsListState({
    // 仅保留需要的 BaseState 字段
    super.netState = NetState.loading,
    super.isNoMoreDataState = false,
    super.dataList = const [],
    // 自身字段
    this.pageNo = 1,
  });

  ActivityRecordsListState copyWith({
    // BaseState
    NetState? netState,
    bool? isNoMoreDataState,
    List<ActivityTaskRecord>? dataList,
    // 自身
    int? pageNo,
  }) {
    return ActivityRecordsListState(
      netState: netState ?? this.netState,
      isNoMoreDataState: isNoMoreDataState ?? this.isNoMoreDataState,
      dataList: dataList ?? this.dataList,
      pageNo: pageNo ?? this.pageNo,
    );
  }

  @override
  List<Object?> get props => [
    netState,
    isNoMoreDataState,
    dataList,
    pageNo,
  ];
}

