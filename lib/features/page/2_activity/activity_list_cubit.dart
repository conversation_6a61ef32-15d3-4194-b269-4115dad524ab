import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/models/apis/user.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/models/view_models/activity_type.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/dialog/check_in_success_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/home/<USER>/home_expandable_activity_card.dart';

part 'activity_list_state.dart';

class ActivityListCubit extends Cubit<ActivityListState> {
  late StreamSubscription<bool> _isLoginSubscription;

  ActivityListCubit() : super(const ActivityListState());

  refreshData() {
    fetchGameActivityCategoryList();
    fetchTaskCategoryList();
    fetchCheckInData();
  }

  reset() {
    emit(const ActivityListState());
    refreshData();
  }

  @override
  Future<void> close() {
    _isLoginSubscription.cancel();
    return super.close();
  }

  void updateIsNoMoreDataState(bool isNoMoreDataState) {
    emit(state.copyWith(isNoMoreDataState: isNoMoreDataState));
  }

  void onChangeTabIndex(int index) {
    emit(state.copyWith(currentTabIndex: index));
    refreshCurrentTabData();
  }

  void onChangeGameTabIndex(int index) {
    emit(state.copyWith(currentGameTabIndex: index));
  }

  void onChangeTaskTabIndex(int index) {
    emit(state.copyWith(currentTaskTabIndex: index));
  }

  /// 页面切换时，刷新当前页数据
  void refreshCurrentTabData() {
    if (state.currentTabIndex == 0) {
      if (state.gameCategoryList.isEmpty) {
        fetchGameActivityCategoryList();
      } else {
        fetchGameListData(state.gameCategoryList[state.currentGameTabIndex]);
      }
    } else if (state.currentTabIndex == 1) {
      if (state.taskCategoryList.isEmpty) {
        fetchTaskCategoryList();
      } else {
        fetchTaskListData(state.taskCategoryList[state.currentTaskTabIndex]);
      }
    }
  }

  Future<void> fetchCheckInData() async {
    try {
      final res = await UserApi.fetchCheckInList();
      // 计算签到统计
      int totalSignedDays = 0;
      double totalGoldEarned = 0;
      if (res != null) {
        for (final entity in res.fullList) {
          if (entity.signInState == SignInState.signed.value) {
            totalSignedDays += 1;
            totalGoldEarned += entity.signInAward;
          } else if (entity.signInState == SignInState.backdated.value) {
            totalSignedDays += 1;
            totalGoldEarned += entity.reSignInAward;
          }
        }
      }
      emit(state.copyWith(
        checkInModel: res,
        totalSignedDays: totalSignedDays,
        totalGoldEarned: totalGoldEarned,
      ));
    } catch (e) {
      LogD('Error fetching daily check in data: $e');
    } finally {
      emit(state.copyWith(
        checkInListNetState:
        state.checkInModel == null ? NetState.showReload : NetState.success,
      ));
    }
  }

  void calculateCheckInDaysTotal() {
    if (state.checkInModel == null) return;
    int totalSignedDays = 0;
    double totalGoldEarned = 0;
    for (var entity in state.checkInModel!.fullList) {
      if (entity.signInState == SignInState.signed.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.signInAward;
      } else if (entity.signInState == SignInState.backdated.value) {
        totalSignedDays += 1;
        totalGoldEarned += entity.reSignInAward;
      }
    }
    emit(state.copyWith(
      totalSignedDays: totalSignedDays,
      totalGoldEarned: totalGoldEarned,
    ));
  }

  Future<void> confirmClearCheckInInfo(DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.copyWith(checkInModel: state.checkInModel)); // 触发刷新
    try {
      await UserApi.clearUserCheckInInfo();
      await fetchCheckInData();
    } catch (e) {
      model.isFetching = false;
      emit(state.copyWith(checkInModel: state.checkInModel));
    }
  }

  Future<void> onClickCheckBtn(BuildContext context, DailyCheckInItem model) async {
    if (model.isFetching) return;
    model.isFetching = true;
    emit(state.copyWith(checkInModel: state.checkInModel));
    try {
      final res = await UserApi.executeCheckIn(
        isBackdate: model.signInState == SignInState.needBackdate.value,
        date: model.day,
      );
      sl<UserCubit>().fetchUserBalance();
      if (res.$1) {
        await fetchCheckInData();
        final award = model.signInState == SignInState.needBackdate.value
            ? model.reSignInAward
            : model.signInAward;
        CheckInSuccessDialog(money: award, days: state.totalSignedDays).show(context);
      } else {
        model.isFetching = false;
        emit(state.copyWith(checkInModel: state.checkInModel));
        CommonDialog.show(
          context: context,
          title: 'act_hint'.tr(),
          content: res.$2,
          showCancelBtn: false,
        );
      }
    } catch (e) {
      model.isFetching = false;
      emit(state.copyWith(checkInModel: state.checkInModel));
    }
  }

  /// *********************************************** 游戏活动相关

  Future<void> fetchGameActivityCategoryList() async {
    final result = await ActivityApi.fetchActivityCategoryList(
      1,
      mapper: ActivityGameTypeViewModel.fromActivityCategory,
    );

    emit(state.copyWith(
      gameCategoryList: result,
      gameNetState: result.isEmpty ? NetState.showReload : NetState.success,
    ));

    if (result.isNotEmpty) {
      await fetchGameListData(result.first);
      emit(state.copyWith(gameNetState: NetState.success));
      unawaited(_preloadOtherCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherCategories(List<ActivityGameTypeViewModel> categories) async {
    try {
      await Future.wait(
        categories.map((category) => fetchGameListData(category)),
        eagerError: false,
      );
    } catch (e) {
      print('Preload error: $e');
    }
  }

  Future<List<ActivityRecords>> fetchGameListData(ActivityGameTypeViewModel viewModel) async {
    viewModel.netState = NetState.loading;
    if (viewModel.list.isEmpty) {
      viewModel.refreshStatus = NetState.loading;
    }
    // 替换列表引用以触发重建
    emit(state.copyWith(gameCategoryList: List<ActivityGameTypeViewModel>.from(state.gameCategoryList)));

    final result = await ActivityApi.fetchActivityList(
      pageNo: viewModel.pageNo,
      activeCategory: viewModel.category,
    );

    if (viewModel.pageNo == 1) {
      viewModel.list = result.records;
    } else {
      viewModel.list = List.from(viewModel.list)..addAll(result.records);
    }

    viewModel.isNoMoreDataState = result.total <= viewModel.list.length;
    viewModel.refreshStatus = NetState.success;
    viewModel.netState = viewModel.list.isEmpty ? NetState.empty : NetState.success;

    emit(state.copyWith(gameCategoryList: List<ActivityGameTypeViewModel>.from(state.gameCategoryList)));
    return result.records;
  }

  /// *********************************************** 自助领取相关

  Future<void> fetchTaskCategoryList() async {
    final result = await ActivityApi.fetchActivityCategoryList(
      2,
      mapper: ActivityTaskTypeViewModel.fromActivityCategory,
    );

    emit(state.copyWith(
      taskCategoryList: result,
      taskNetState: result.isEmpty ? NetState.showReload : NetState.success,
    ));

    if (result.isNotEmpty && sl<UserCubit>().state.isLogin) {
      await fetchTaskListData(result.first);
      emit(state.copyWith(taskNetState: NetState.success));
      unawaited(_preloadOtherTaskCategories(result.skip(1).toList()));
    }
  }

  Future<void> _preloadOtherTaskCategories(List<ActivityTaskTypeViewModel> categories) async {
    try {
      await Future.wait(
        categories.map((category) => fetchTaskListData(category)),
        eagerError: false,
      );
    } catch (e) {
      print('task Preload error: $e');
    }
  }

  Future<List<ActivityTask>> fetchTaskListData(ActivityTaskTypeViewModel viewModel) async {
    viewModel.netState = NetState.loading;
    if (viewModel.list.isEmpty) {
      viewModel.netState = NetState.loading;
    }
    emit(state.copyWith(taskCategoryList: List<ActivityTaskTypeViewModel>.from(state.taskCategoryList)));

    final result = await ActivityApi.fetchTaskList(activeCategory: viewModel.category);

    final inProgressTask = result.firstWhereOrNull((task) => task.receiveStatus == 1);
    final processedTasks = <ActivityTask>[
      if (viewModel.category != 1)
        if (inProgressTask != null) ...[inProgressTask.clone()..isProcess = true],
      ...result,
    ];

    viewModel.list = processedTasks;
    viewModel.netState = result.isNotEmpty ? NetState.success : NetState.empty;

    emit(state.copyWith(taskCategoryList: List<ActivityTaskTypeViewModel>.from(state.taskCategoryList)));
    return result;
  }


  bool isRequestCompleteTask = false;

  onClickCompleteTask({required int id, required int category}) async {
    if (isRequestCompleteTask) return;
    isRequestCompleteTask = true;
    GSEasyLoading.showLoading();
    try {
      final flag = await ActivityApi.completeActivityTask(id: id);
      if (flag) {
        final viewModel = state.taskCategoryList.firstWhereOrNull((e) => e.category == category);
        if (viewModel != null) fetchTaskListData(viewModel);
      }
    } finally {
      isRequestCompleteTask = false;
      GSEasyLoading.dismiss();
    }
  }
}
