import 'package:easy_localization/easy_localization.dart';
import 'package:bloc/bloc.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/apis/activity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/easy_loading.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/models/apis/user.dart';
import 'activity_detail_state.dart';

class ActivityDetailCubit extends Cubit<ActivityDetailState> {
  ActivityDetailCubit() : super(const ActivityDetailState().init()) {
    sl<UserCubit>().fetchUserInfo();
  }

  void setPhoneNo(String phoneNo) => emit(state.copyWith(phoneNo: phoneNo));

  fetchCollectStatus(int id) async {
    GSEasyLoading.showLoading();
    final res = await ActivityApi.fetchActivityCollectionStatus(id: id);
    GSEasyLoading.dismiss();
    emit(state.copyWith(collectStatus: ActivityRewardStatusExtension.fromValue(res)));
  }

  executeCollect(context, int id, int recordType) async {
    GSEasyLoading.showLoading();
    emit(state.copyWith(executeStatus: NetState.loading));

    // Map recordType to operationType
    int operationType;
    switch (recordType) {
      case 0: // Notice
        operationType = 1; // Acknowledge
        break;
      case 1: // Bonus
        operationType = 2; // Collect
        break;
      case 2: // Application
        operationType = 3; // Apply
        break;
      default:
        GSEasyLoading.dismiss();
        GSEasyLoading.showToast("Invalid activity type");
        return;
    }

    final res = await ActivityApi.applyActivity(id: id, operationType: operationType);
    emit(state.copyWith(executeStatus: NetState.success));
    // Check status after apply for both bonus and application
    if (recordType == 1 || recordType == 2) {
      await fetchCollectStatus(id);
    }

    // Update balance only for bonus
    if (recordType == 1) {
      sl<UserCubit>().fetchUserBalance();
    }

    GSEasyLoading.dismiss();
    if (res.isSuccess) {
      String message;
      switch (operationType) {
        case 1:
          message = 'act_confirmed'.tr(); // Confirmed (for Notice type activities)
          break;
        case 2:
          message = "act_receive_success".tr(); // Successfully collected (for Bonus type activities)
          break;
        case 3:
          message = "act_apply_success".tr(); // Successfully applied (for Application type activities)
          break;
        default:
          message = "act_operation_success功".tr(); // Generic success message for unexpected operation types
      }
      GSEasyLoading.showToast(message);
    } else {
      if (res.msg.isNotEmpty) {
        CommonDialog.show(
            context: context,
            title: "act_hint".tr(),
            content: res.msg,
            sureBtnTitle: "act_got_it".tr(),
            showCancelBtn: false,
            complete: () => {});
      }
    }
  }

  void verifyCode({required String phoneNo, required String code}) async {
    if (phoneNo.isEmpty) {
      GSEasyLoading.showToast('hint_enter_phone'.tr());
      return;
    }
    // Validate phone number length and format
    final bool isValidLength = phoneNo.length >= 6;
    // final bool isValidFormat = RegExp(r'^1[3-9]\d{9}$').hasMatch(phoneNo); // No need

    if (!isValidLength) {
      GSEasyLoading.showToast('please_enter_correct_phone'.tr());
      return;
    }

    if (code.isEmpty) {
      GSEasyLoading.showToast('please_enter_verification_code'.tr());
      return;
    }

    GSEasyLoading.showLoading();
    emit(state.copyWith(bindStatus: NetState.loading));
    final result = await UserApi.bindPhoneNo(phoneNo, code);
    if (result) {
      emit(state.copyWith(isVerified: true, bindStatus: NetState.success));
    } else {
      emit(state.copyWith(isVerified: false, bindStatus: NetState.failed));
    }
    GSEasyLoading.dismiss();
  }
}
