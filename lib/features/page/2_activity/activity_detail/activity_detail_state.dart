import 'package:equatable/equatable.dart';
import 'package:wd/core/constants/enums.dart';

import '../../../../core/base/base_state.dart';

class ActivityDetailState extends Equatable {
  final ActivityRewardStatus collectStatus;
  final NetState updateStatus;
  final NetState bindStatus; 
  final NetState executeStatus;
  final bool isVerified;
  final String phoneNo;

  const ActivityDetailState({
    this.collectStatus = ActivityRewardStatus.unknown,
    this.updateStatus = NetState.idle,
    this.bindStatus = NetState.idle,
    this.executeStatus = NetState.idle,
    this.isVerified = false,
    this.phoneNo = '',
  });

  ActivityDetailState init() {
    return const ActivityDetailState();
  }

  @override
  List<Object?> get props => [
    collectStatus,
    updateStatus,
    bindStatus,
    executeStatus,
    isVerified,
    phoneNo,
  ];

  ActivityDetailState copyWith({
    ActivityRewardStatus? collectStatus,
    NetState? updateStatus,
    NetState? bindStatus,
    NetState? executeStatus,
    bool? isVerified,
    String? phoneNo,
  }) {
    return ActivityDetailState(
      collectStatus: collectStatus ?? this.collectStatus,
      updateStatus: updateStatus ?? this.updateStatus,
      bindStatus: bindStatus ?? this.bindStatus,
      executeStatus: executeStatus ?? this.executeStatus,
      isVerified: isVerified ?? this.isVerified,
      phoneNo: phoneNo ?? this.phoneNo,
    );
  }
}
