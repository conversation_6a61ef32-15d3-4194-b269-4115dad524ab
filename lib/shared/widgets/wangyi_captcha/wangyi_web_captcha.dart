import 'dart:html' as html;
import 'dart:ui_web' as ui;
import 'package:flutter/material.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:rxdart/rxdart.dart';
import 'package:wd/core/utils/locale/locale_util.dart';

typedef StringCallBack = void Function(String str);

class WangYiCaptcha {
  void show({
    required String account,
    required String captchaId,
    required StringCallBack onSuccess,
    VoidCallback? onValidateFailClose,
    VoidCallback? onError,
  }) {
    showDialog(
      context: sl<NavigatorService>().navigatorKey.currentContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _WangYiCaptchaWidget(
          account: account,
          captchaId: captchaId,
          onSuccess: onSuccess,
          onValidateFailClose: onValidateFailClose,
          onError: onError,
        );
      },
    );
  }
}

class _WangYiCaptchaWidget extends StatefulWidget {
  final String account;
  final String captchaId;
  final StringCallBack onSuccess;
  final VoidCallback? onValidateFailClose;
  final VoidCallback? onError;

  const _WangYiCaptchaWidget({
    required this.account,
    required this.captchaId,
    required this.onSuccess,
    this.onValidateFailClose,
    this.onError,
  });

  @override
  _WangYiCaptchaWidgetState createState() => _WangYiCaptchaWidgetState();
}

class _WangYiCaptchaWidgetState extends State<_WangYiCaptchaWidget> {
  final String viewType = 'captcha-container-${DateTime.now().millisecondsSinceEpoch}';
  final String minuteTimestamp = (DateTime.now().millisecondsSinceEpoch ~/ 60000).toString();
  bool _isInitialized = false;
  html.IFrameElement? _iframe;
  final _messageController = PublishSubject<Map>();

  /// Convert Flutter locale to Wangyi captcha language format
  String _getWangyiLanguage() {
    try {
      final locale = LocaleUtil().flutterLocale;
      final languageCode = locale.languageCode;
      final countryCode = locale.countryCode;

      // Map Flutter locale to Wangyi captcha supported languages
      if (languageCode == 'zh') {
        if (countryCode == 'TW' || countryCode == 'HK') {
          return 'zh-TW'; // Traditional Chinese
        }
        return 'zh-CN'; // Simplified Chinese (default)
      } else if (languageCode == 'en') {
        return 'en'; // English
      } else if (languageCode == 'ja') {
        return 'ja'; // Japanese
      } else if (languageCode == 'ko') {
        return 'ko'; // Korean
      } else if (languageCode == 'th') {
        return 'th'; // Thai
      } else if (languageCode == 'vi') {
        return 'vi'; // Vietnamese
      } else if (languageCode == 'fr') {
        return 'fr'; // French
      } else if (languageCode == 'ru') {
        return 'ru'; // Russian
      } else if (languageCode == 'ar') {
        return 'ar'; // Arabic
      }

      // Default to simplified Chinese if language not supported
      return 'zh-CN';
    } catch (e) {
      // Fallback to simplified Chinese if locale is not available
      return 'zh-CN';
    }
  }

  @override
  void initState() {
    super.initState();
    _setupMessageListener();
    _initIframe();
  }

  bool isSuccess = false;
  void _setupMessageSubscription() {
    _messageController.debounceTime(const Duration(milliseconds: 500)).listen((data) {
      if (data['type'] == 'captcha_verify') {
        if (data['error'] != null) {
          widget.onError?.call();
          if (mounted) Navigator.of(context).pop();
        } else if (data['validate'] != null) {
          isSuccess = true;
          widget.onSuccess.call(data['validate']);
          if (mounted) Navigator.of(context).pop();
        }
      } else if (data['type'] == 'close' || data['type'] == 'close_verify') {
        // 处理用户手动关闭验证码的情况
        if (!isSuccess) {
          widget.onValidateFailClose?.call();
        }
        if (mounted) Navigator.of(context).pop();
      }
    });
  }

  void _setupMessageListener() {
    try {
      html.window.addEventListener('message', (html.Event event) {
        final messageEvent = event as html.MessageEvent;

        if (messageEvent.data is Map) {
          _messageController.add(messageEvent.data as Map);
        }
      });
    } catch (e) {
      print("Error setting up message listener: $e");
    }
  }

  @override
  void dispose() {
    _messageController.close();
    _iframe?.remove();
    super.dispose();
  }

  Future<void> _initIframe() async {
    try {
      final String htmlContent = await rootBundle.loadString('assets/html/captcha/wy_captcha.html');

      final String replacedContent = htmlContent
          .replaceAll('<CAPTCHA_ID>', widget.captchaId)
          .replaceAll('<MINUTE_TIMESTAMP>', minuteTimestamp)
          .replaceAll('<IS_DEBUG>', kDebug ? 'true' : 'false')
          .replaceAll('<LANGUAGE>', _getWangyiLanguage());

      ui.platformViewRegistry.registerViewFactory(viewType, (int viewId) {
        _iframe = html.IFrameElement()
          ..style.border = 'none'
          ..style.width = '100%'
          ..style.height = '100%'
          ..srcdoc = replacedContent;
        return _iframe!;
      });

      _setupMessageSubscription();
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      print('Error loading HTML: $e');
      widget.onError?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return SizedBox(
      width: double.infinity,
      height: 400,
      child: HtmlElementView(
        viewType: viewType,
      ),
    );
  }
}
