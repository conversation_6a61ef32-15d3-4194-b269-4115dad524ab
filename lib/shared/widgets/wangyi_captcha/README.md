# WangYi Captcha Language Support

This document describes the language translation functionality implemented for the WangYi captcha widget.

## Overview

The WangYi captcha widget now automatically adapts to the user's selected language using the `LocaleUtil` from the app's internationalization system.

## Supported Languages

The WangYi captcha supports the following languages:

- **zh-CN**: Simplified Chinese (default)
- **zh-TW**: Traditional Chinese
- **en**: English
- **ja**: Japanese
- **ko**: Korean
- **th**: Thai
- **vi**: Vietnamese
- **fr**: French
- **ru**: Russian
- **ar**: Arabic

## Implementation Details

### Web Platform (`wangyi_web_captcha.dart`)

1. **Language Detection**: The `_getWangyiLanguage()` method converts the current Flutter locale to the appropriate WangYi captcha language code.

2. **HTML Configuration**: The language parameter is injected into the HTML template using the `<LANGUAGE>` placeholder.

3. **Captcha Initialization**: The `lang` parameter is added to the captcha configuration object in the HTML file.

### Mobile Platform (`wangyi_app_captcha.dart`)

1. **Language Detection**: Similar `_getWangyiLanguage()` method for mobile platform.

2. **Plugin Configuration**: The `lang` parameter is added to the captcha plugin initialization parameters.

## Language Mapping Logic

The language mapping follows this priority:

1. **Chinese Languages**:
   - `zh-TW` or `zh-HK` → `zh-TW` (Traditional Chinese)
   - `zh-CN` or other `zh` variants → `zh-CN` (Simplified Chinese)

2. **Other Languages**: Direct mapping (e.g., `en` → `en`, `ja` → `ja`)

3. **Fallback**: Unsupported languages default to `zh-CN` (Simplified Chinese)

## Usage

The language translation is automatic and requires no additional configuration. The captcha will use the language set in the app's `LocaleUtil`.

### Example Usage

```dart
// The captcha automatically uses the current app language
WangYiCaptcha().show(
  account: userAccount,
  captchaId: kWangYiVerityKey,
  onSuccess: (validate) {
    // Handle success
  },
  onError: () {
    // Handle error
  },
);
```

## Testing

Unit tests are provided in `test/wangyi_captcha_language_test.dart` to verify the language conversion logic.

Run tests with:
```bash
flutter test test/wangyi_captcha_language_test.dart
```

## Translation Keys

Additional captcha-related translation keys have been added to the translation files:

- `captcha_loading`: "Loading captcha..." / "验证码加载中..."
- `captcha_error`: "Captcha error" / "验证码错误"
- `captcha_timeout`: "Captcha timeout" / "验证码超时"

## Notes

- The language is determined at the time the captcha is initialized
- Language changes require reinitialization of the captcha
- Unsupported languages gracefully fall back to Simplified Chinese
- The implementation is compatible with both web and mobile platforms
