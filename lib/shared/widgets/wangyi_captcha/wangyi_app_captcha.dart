import 'package:flutter/material.dart';
import 'package:captcha_plugin_flutter/captcha_plugin_flutter.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/core/utils/locale/locale_util.dart';

typedef StringCallBack = void Function(String str);

class WangYiCaptcha {
  late CaptchaPluginFlutter captchaPlugin;
  bool isShowing = false;

  WangYiCaptcha() {
    captchaPlugin = CaptchaPluginFlutter();
  }

  /// Convert Flutter locale to Wangyi captcha language format
  String _getWangyiLanguage() {
    try {
      final locale = LocaleUtil().flutterLocale;
      final languageCode = locale.languageCode;
      final countryCode = locale.countryCode;

      // Map Flutter locale to Wangyi captcha supported languages
      if (languageCode == 'zh') {
        if (countryCode == 'TW' || countryCode == 'HK') {
          return 'zh-TW'; // Traditional Chinese
        }
        return 'zh-CN'; // Simplified Chinese (default)
      } else if (languageCode == 'en') {
        return 'en'; // English
      } else if (languageCode == 'ja') {
        return 'ja'; // Japanese
      } else if (languageCode == 'ko') {
        return 'ko'; // Korean
      } else if (languageCode == 'th') {
        return 'th'; // Thai
      } else if (languageCode == 'vi') {
        return 'vi'; // Vietnamese
      } else if (languageCode == 'fr') {
        return 'fr'; // French
      } else if (languageCode == 'ru') {
        return 'ru'; // Russian
      } else if (languageCode == 'ar') {
        return 'ar'; // Arabic
      }

      // Default to simplified Chinese if language not supported
      return 'zh-CN';
    } catch (e) {
      // Fallback to simplified Chinese if locale is not available
      return 'zh-CN';
    }
  }

  void show({
    required String captchaId,
    required String account,
    required StringCallBack onSuccess,
    VoidCallback? onValidateFailClose,
    VoidCallback? onError,
  }) {
    if (isShowing) return;

    isShowing = true;
    bool isSuccess = false;

    captchaPlugin.init({
      "captcha_id": captchaId,
      "is_debug": false,
      "dimAmount": 0.5,
      "lang": _getWangyiLanguage(),
    });

    captchaPlugin.showCaptcha(
      onLoaded: () {},
      onSuccess: (dynamic data) {
        isShowing = false;
        if (data is! Map) return;
        var validate = data["validate"];
        if (validate == null || StringUtil.isEmpty(validate)) {
          return GSEasyLoading.showToast(data["message"]);
        }
        isSuccess = true;
        onSuccess(validate);
      },
      onClose: (dynamic data) {
        isShowing = false;
        if (!isSuccess) {
          onValidateFailClose?.call();
        }
      },
      onError: (dynamic data) {
        isShowing = false;
        onError?.call();
      },
    );
  }
}
