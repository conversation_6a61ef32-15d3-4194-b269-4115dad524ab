import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/entities/lottery_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import '../../../core/base/base_state.dart';
import '../../../core/base/empty_widget.dart';
import '../../../features/page/0_home/home_lottery/lottery_detail/lottery_detail_cubit.dart';
import '../../../features/page/0_home/home_lottery/lottery_detail/lottery_detail_state.dart';
import '../blur_container.dart';

class LotteryGameFilterDrawer extends StatefulWidget {
  final int currentId;
  final List<LotteryGroup> lotteryGroupList;
  final void Function(Lottery) onClickSure;

  const LotteryGameFilterDrawer({
    super.key,
    required this.currentId,
    required this.lotteryGroupList,
    required this.onClickSure,
  });

  @override
  State<StatefulWidget> createState() => _LotteryGameFilterDrawerState();
}

class _LotteryGameFilterDrawerState extends State<LotteryGameFilterDrawer> {
  late List<LotteryGroup> dataList;

  String currentWayCode = ""; // 当前选中的交易方式的code

  @override
  void initState() {
    dataList = widget.lotteryGroupList
        .where((group) => group.list.isNotEmpty)
        .toList();
    context.read<LotteryDetailCubit>().fetchLotteryRule(widget.currentId);
    super.initState();
  }

  onTapItem(Lottery model) => widget.onClickSure(model);

  /// Determines if valid game rules are available
  ///
  /// Returns true only when:
  /// - Network state is successful AND
  /// - Game rule content is not empty
  ///
  /// This ensures tabs are only shown when we have confirmed valid content
  bool _hasGameRules(LotteryDetailState state) {
    // Check for explicit failure states
    if (_isGameRuleFailureState(state.gameRuleNetState)) {
      return false;
    }

    // Only return true for successful state with actual content
    return state.gameRuleNetState == NetState.success &&
           state.gameRule.trim().isNotEmpty;
  }

  /// Check if the game rule network state indicates failure or no data
  bool _isGameRuleFailureState(NetState netState) {
    return netState == NetState.empty ||
           netState == NetState.failed;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(
        15.gw,
        max(MediaQuery.of(context).padding.top, 10.gw),
        15.gw,
        max(MediaQuery.of(context).padding.bottom, 10.gw),
      ),
      child: BlocBuilder<LotteryDetailCubit, LotteryDetailState>(
        builder: (context, state) => _buildContent(state),
      ),
    );
  }

  /// Build the main content based on the current state
  Widget _buildContent(LotteryDetailState state) {
    final bool hasGameRules = _hasGameRules(state);
    final bool isLoading = state.gameRuleNetState == NetState.loading;

    return ListView(
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        if (isLoading)
          _buildLoadingSection()
        else if (hasGameRules)
          _buildWithGameRules(state)
        else
          _buildWithoutGameRules(),
      ],
    );
  }

  /// Build loading section with a nice loader
  Widget _buildLoadingSection() {
    return Column(
      children: [
        SizedBox(height: 40.gw),
        Center(
          child: Column(
            children: [
              SizedBox(
                width: 24.gw,
                height: 24.gw,
                child: CircularProgressIndicator(
                  strokeWidth: 2.gw,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
              SizedBox(height: 12.gw),
              Text(
                '正在加载游戏规则...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xff666D81),
                    ),
              ),
            ],
          ),
        ),
        SizedBox(height: 40.gw),
      ],
    );
  }

  /// Build content when game rules are available
  Widget _buildWithGameRules(LotteryDetailState state) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 50.gw),
          child: _GameTab(
            currentTabIndex: state.gameTabIndex,
            onChangeTabIndex: (index) =>
                context.read<LotteryDetailCubit>().changeTab(index),
          ),
        ),
        SizedBox(height: 15.gw),
        if (state.gameTabIndex == 0) _gameDetail(state) else _moreGames(),
      ],
    );
  }

  /// Build content when no game rules are available
  Widget _buildWithoutGameRules() {
    return _moreGames();
  }

  /// game rules
  Widget _gameDetail(LotteryDetailState state) {
    if (state.gameRuleNetState == NetState.loading) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 100.gw),
          child: const CircularProgressIndicator(),
        ),
      );
    }
    if (state.gameRuleNetState == NetState.empty ||
        state.gameRuleNetState == NetState.failed) {
      return const EmptyWidget();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
      margin: EdgeInsets.only(bottom: 10.gw),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: const Color(0xFFECECEC),
          width: .5.gw,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xff000000).withOpacity(.1),
            offset: const Offset(0, 2),
            blurRadius: 2,
          ),
        ],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (state.gameRule.isNotEmpty)
            Text(
              state.gameRule,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xff666D81),
                  ),
            )
          else
            const EmptyWidget()
        ],
      ),
    );
  }

  /// more games
  ListView _moreGames() => ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) => _buildGroupWidget(dataList[index]),
      separatorBuilder: (context, index) => const SizedBox(height: 10),
      itemCount: dataList.length);

  /// group widget
  Widget _buildGroupWidget(LotteryGroup group) => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(group.groupName,
              style: Theme.of(context).textTheme.headlineMedium),
          SizedBox(height: 8.gw),
          Wrap(
            runSpacing: 10,
            spacing: 10,
            children: group.list
                .map((e) => _buildFilterItem(
                      title: e.lotteryName,
                      isSel: e.id == widget.currentId,
                      onTap: () => onTapItem(e),
                    ))
                .toList(),
          )
        ],
      );

  Widget _buildFilterItem({
    required String title,
    required bool isSel,
    required GestureTapCallback onTap,
  }) =>
      GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 5.gw),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3.gw),
              color: isSel ? Theme.of(context).primaryColor : Colors.white,
              border: Border.all(
                color: isSel
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).dividerColor,
                width: 1,
              )),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: isSel ? Colors.white : null,
                  fontWeight: isSel ? FontWeight.w400 : null,
                ),
          ),
        ),
      );
}

class _GameTab extends StatelessWidget {
  final int currentTabIndex;
  final Function(int) onChangeTabIndex;
  const _GameTab(
      {required this.currentTabIndex, required this.onChangeTabIndex});
  static const List<String> tabs = ['游戏规则', '更多游戏'];
  @override
  Widget build(BuildContext context) => BlurContainer(
        height: 35.gw,
        borderRadius: BorderRadius.circular(8.gw),
        borderColor: Colors.white,
        borderWidth: 1,
        gradientColors: const [
          Color(0xFFECEFF6),
          Color(0xFFFEFEFF),
        ],
        boxShadow: const BoxShadow(
          color: Color(0x1A000000), // 阴影颜色，带透明度
          offset: Offset(0, 2), // 阴影偏移
          blurRadius: 2, // 模糊半径
        ),
        child: Row(
          children: tabs
              .map((tab) => _buildTabButton(
                  text: tab,
                  index: tabs.indexOf(tab),
                  currentTabIndex: currentTabIndex,
                  onChangeTabIndex: onChangeTabIndex))
              .toList(),
        ),
      );

  Widget _buildTabButton(
          {required String text,
          required int index,
          required int currentTabIndex,
          required Function(int) onChangeTabIndex}) =>
      Expanded(
          child: SizedBox(
        height: 35.gw,
        child: GestureDetector(
          onTap: () => onChangeTabIndex(index),
          child: Container(
            height: 35.gw,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.gw),
              gradient: index == currentTabIndex
                  ? const LinearGradient(
                      colors: [Color(0xFFD8C6B4), Color(0xFFB8997B)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    )
                  : null,
            ),
            child: Center(
              child: Text(
                text,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: index == currentTabIndex ? Colors.white : Colors.black,
                  fontSize: 14.fs,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ));
}
