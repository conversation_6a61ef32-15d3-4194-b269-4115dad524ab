import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

class CommonCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final String text;
  final TextStyle? textStyle;
  final String selectedIconPath;
  final String unselectedIconPath;

  const CommonCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
    required this.text,
    this.textStyle,
    this.selectedIconPath = 'assets/images/common/icon_check_rect_selected.svg',
    this.unselectedIconPath = 'assets/images/common/icon_check_rect.svg',
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged(!value);
      },
      child: Row(
        children: [
          SvgPicture.asset(
            value ? selectedIconPath : unselectedIconPath,
            width: 20.gw,
            height: 20.gw,
          ),
          const SizedBox(width: 8.0),
          Text(
            text,
            style: textStyle ?? Theme.of(context).textTheme.headlineSmall?.copyWith(fontSize: 14.fs),
          ),
        ],
      ),
    );
  }
}
