import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/game_home_util.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/hot_push_image.dart';
import 'package:wd/shared/widgets/platform/platform_list_cell.dart';
import 'package:wd/shared/widgets/platform/platform_list_cell_horizon.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

enum GameHomePopularType {
  popular('home_header_title_popular'),
  recent('home_header_title_recent'),
  favorites('home_header_title_favorites');

  final String key;

  const GameHomePopularType(this.key);

  String get title => key.tr();

  String get tabImagePath {
    switch (this) {
      case GameHomePopularType.popular:
        return "assets/images/home/<USER>";
      case GameHomePopularType.recent:
        return "assets/images/home/<USER>";
      case GameHomePopularType.favorites:
        return "assets/images/home/<USER>";
    }
  }


  String get tabTitle {
    switch (this) {
      case GameHomePopularType.popular:
        return 'game_home_popular'.tr();
      case GameHomePopularType.recent:
        return 'game_home_recent'.tr();
      case GameHomePopularType.favorites:
        return 'game_home_favorites'.tr();
    }
  }


  String get emptyTipText {
    switch (this) {
      case GameHomePopularType.popular:
        return 'no_data'.tr();
      case GameHomePopularType.recent:
        return 'empty_recent'.tr();
      case GameHomePopularType.favorites:
        return 'empty_favorites'.tr();
    }
  }

  String get gameTitleIconPath {
    switch (this) {
      case GameHomePopularType.popular:
      case GameHomePopularType.recent:
        return "assets/images/home/<USER>";
      case GameHomePopularType.favorites:
        return "assets/images/home/<USER>";
    }
  }

  String get venueTitleIconPath {
    switch (this) {
      case GameHomePopularType.popular:
      case GameHomePopularType.recent:
      case GameHomePopularType.favorites:
        return "assets/images/home/<USER>";
    }
  }

}

class GameHomePopularListView extends StatefulWidget {
  /// 游戏
  final List<GameV2>? gameList;

  /// 场馆
  final List<GamePlatformV2>? venueList;

  /// 类型：热门/最近/收藏
  final GameHomePopularType type;

  /// 点击事件回调
  final Function(GameV2) onGameTap;
  final Function(GamePlatformV2) onVenueTap;
  final Function(GameV2) onGameFavTap;

  const GameHomePopularListView({
    super.key,
    this.gameList,
    this.venueList,
    required this.type,
    required this.onGameTap,
    required this.onVenueTap,
    required this.onGameFavTap,
  });

  @override
  State<StatefulWidget> createState() => _GameHomePopularListViewState();
}

class _GameHomePopularListViewState extends State<GameHomePopularListView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if ((widget.gameList == null || widget.gameList!.isEmpty) &&
            (widget.venueList == null || widget.venueList!.isEmpty)) ...[
          _buildEmptyTipsWidget(),
        ],
        _buildGameList(),
        _buildVenueList(),
      ],
    );
  }

  _buildEmptyTipsWidget() {
    return Container(
      alignment: Alignment.center,
      height: 100.gw,
      child: Text(
        widget.type.emptyTipText,
        style: TextStyle(
          fontSize: 14.fs,
          color: const Color(0xff808C9F),
        ),
      ),
    );
  }

  Widget _buildGameList() {
    if (widget.gameList == null || widget.gameList!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 13.gw),
        _buildSectionHeader(
          titleA: widget.type.title,
          titleB: 'game'.tr(),
          iconPath: widget.type.gameTitleIconPath,
        ),
        SizedBox(height: 16.gw),
        GridView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: GameHomeUtil.popularGameGridViewCrossAxisCount,
            childAspectRatio: GameHomeUtil.popularGameGridViewAspectRatio,
            crossAxisSpacing: GameHomeUtil.popularGameGridViewCrossAxisSpacing,
            mainAxisSpacing: GameHomeUtil.popularGameGridViewMainAxisSpacing,
          ),
          itemCount: widget.gameList!.length,
          itemBuilder: (context, index) {
            final game = widget.gameList![index];

            return AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 375),
              columnCount: GameHomeUtil.popularGameGridViewCrossAxisCount,
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: HomeGameCell(
                    title: game.name,
                    iconUrl: GlobalConfig().systemConfig.gamePicBaseUrl + game.mainImgUrl,
                    platIcon: GlobalConfig().systemConfig.gamePicBaseUrl + game.platIcon,
                    imageFit: BoxFit.cover,
                    isFav: game.isSavour,
                    onTap: () => widget.onGameTap.call(game),
                    onTapFav: () => widget.onGameFavTap.call(game),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildVenueList() {
    // 收藏类型不显示场馆列表
    if (widget.type == GameHomePopularType.favorites || widget.venueList == null || widget.venueList!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 13.gw),

        _buildSectionHeader(
          titleA: widget.type.title,
          titleB: 'venue'.tr(),
          iconPath: widget.type.venueTitleIconPath,
        ),
        SizedBox(height: 16.gw),
        GridView.builder(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: GameHomeUtil.gameGridViewCrossAxisCount,
            childAspectRatio: GameHomeUtil.gameGridViewAspectRatio,
            crossAxisSpacing: GameHomeUtil.gameGridViewCrossAxisSpacing,
            mainAxisSpacing: GameHomeUtil.gameGridViewMainAxisSpacing,
          ),
          itemCount: widget.venueList!.length,
          itemBuilder: (context, index) {
            final venue = widget.venueList![index];
            return AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 375),
              columnCount: GameHomeUtil.gameGridViewCrossAxisCount,
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: kChannel == 'JS'
                      ? PlatformListCellHorizon(
                          model: PlatformListCellViewModel.fromAny(platform: venue),
                          onTap: () => widget.onVenueTap.call(venue),
                        )
                      : PlatformListCell(
                          model: PlatformListCellViewModel.fromAny(platform: venue),
                          onTap: () => widget.onVenueTap.call(venue),
                        ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSectionHeader({
    required String titleA,
    required String titleB,
    required String iconPath,
  }) {
    return SizedBox(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          HotPushImage(
            imagePath: iconPath,
            height: 20.gw,
          ),
          SizedBox(width: 8.gw),
          AneText(
            titleA,
            style: context.textTheme.primary.fs20.w500,
          ),
          AneText(
            titleB,
            style: context.textTheme.secondary.fs20.w500,
          ),
        ],
      ),
    );
  }
}
