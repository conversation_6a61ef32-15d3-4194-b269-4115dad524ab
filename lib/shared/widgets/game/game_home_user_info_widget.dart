import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/game_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/home/<USER>';
import 'package:wd/shared/widgets/text/ane_text.dart';

class GameHomeUserInfoWidget extends StatefulWidget {
  final bool showLoginTips;
  final Color textColor;
  final double? height;
  final EdgeInsets? margin;
  final GestureTapCallback onTapRefreshBalance;

  const GameHomeUserInfoWidget({
    super.key,
    this.showLoginTips = false,
    this.textColor = Colors.white,
    this.height,
    this.margin,
    required this.onTapRefreshBalance,
  });

  @override
  State<StatefulWidget> createState() => _GameHomeUserInfoWidgetState();
}

class _GameHomeUserInfoWidgetState extends State<GameHomeUserInfoWidget> with SingleTickerProviderStateMixin {
  late AnimationController _balanceRefreshController;

  @override
  void initState() {
    _balanceRefreshController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
    super.initState();
  }

  @override
  void dispose() {
    _balanceRefreshController.dispose();
    super.dispose();
  }

  void _handleWalletRefresh() async {
    _balanceRefreshController.repeat();
    await GameUtil.transferOutAllPlatform();
    sl<UserCubit>().fetchUserVip();
    _balanceRefreshController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<UserCubit, UserState, ({bool isLogin, String userName, String avatarUrl, double balance})>(
      selector: (state) => (
        isLogin: state.isLogin,
        userName: state.userInfo?.nickName ?? '',
        avatarUrl: state.userInfo?.faceId.toString() ?? '',
        balance: state.balanceInfo?.accountMoney ?? 0,
      ),
      builder: (context, state) {
        return Container(
          margin: widget.margin,
          height: widget.height,
          width: 100,
          padding: EdgeInsets.only(left: 14.gw, right: 12.gw),
          decoration: BoxDecoration(
            color: context.colorTheme.foregroundColor,
            borderRadius: BorderRadius.all(Radius.circular(10.gw)),
            // border: Border.all(color: context.theme.dividerColor, width: 5),
            border: Border.all(color: context.colorTheme.borderB, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: _buildLeftSection(
                context,
                isLogin: state.isLogin,
                avatarUrl: state.avatarUrl,
                userName: state.userName,
                balance: state.balance,
              )),
              // Refresh button

              SizedBox(width: 8.gw),
              SvgPicture.asset("assets/images/home/<USER>", height: widget.height, width: 1),
              SizedBox(width: 12.gw),
              _buildBtnItem(
                context,
                title: "deposit".tr(),
                imagePath: "assets/images/transact/v3/icon_balance.png",
                onTap: () {
                  AuthUtil.checkIfLogin(() {
                    sl<MainScreenCubit>().goToTransactPage(isDeposit: true);
                  });
                },
              ),
              SizedBox(width: 8.gw),
              _buildBtnItem(
                context,
                imagePath: "assets/images/home/<USER>",
                title: "cash_out".tr(),
                onTap: () {
                  AuthUtil.checkIfLogin(() {
                    sl<MainScreenCubit>().goToTransactPage(isDeposit: false);
                  });
                },
              ),
              SizedBox(width: 8.gw),
              _buildBtnItem(
                context,
                title: "VIP",
                imagePath: "assets/images/home/<USER>",
                onTap: () {
                  AuthUtil.checkIfLogin(() {
                    sl<NavigatorService>().push(AppRouter.vipCenter);
                  });
                },
              ),
            ],
          ),
        );
      },
    );
  }

  _buildLeftSection(
    BuildContext context, {
    required bool isLogin,
    required String avatarUrl,
    required String userName,
    required double balance,
  }) {
    if (!isLogin) {
      return widget.showLoginTips ? _getLoginTipsWidget() : const HomeLoginOperateWidget(); // 登录注册按钮
    }
    return Row(
      children: [
        AuthUtil.getAvatarWidget(context, avatarStr: avatarUrl, size: Size(47.gw, 50.gw)),
        SizedBox(width: 12.gw),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  width: 0.21.gsw,
                  child: AneText(
                    userName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.primary.copyWith(color: context.theme.primaryColor),
                  ),
                ),
                if (isLogin)
                  GestureDetector(
                    onTap: _handleWalletRefresh,
                    child: RotationTransition(
                      turns: _balanceRefreshController.drive(
                        CurveTween(curve: Curves.easeInOutCubic),
                      ),
                      child: Image.asset(
                        "assets/images/mine/icon_mine_refresh.png",
                        width: 24.gw,
                        height: 24.gw,
                      ),
                    ),
                  ),
              ],
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 16.gw,
                  height: 16.gw,
                  decoration: BoxDecoration(
                    color: context.theme.bottomNavigationBarTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(8.gw),
                    border: Border.all(width: 1, color: context.colorTheme.borderC),
                  ),
                  alignment: Alignment.center,
                  child: AneText("\$",
                      style: context.textTheme.primary.fs8
                          .copyWith(color: context.theme.bottomNavigationBarTheme.unselectedItemColor)),
                ),
                SizedBox(width: 6.gw),
                AneText(
                  balance.formattedMoney,
                  style: context.textTheme.primary.fs16.w600,
                )
              ],
            )
          ],
        ),
      ],
    );
  }

  _getLoginTipsWidget() {
    return Padding(
      padding: EdgeInsets.only(right: 16.gw),
      child: InkWell(
        onTap: () {
          AuthUtil.checkIfLogin(() {});
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AutoSizeText(
              "not_logged_in".tr(),
              style: context.textTheme.primary.fs20.w500,
              maxLines: 1,
            ),
            AutoSizeText(
              "login_register_view".tr(),
              style: context.textTheme.title,
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }

  _buildBtnItem(BuildContext context, {required String imagePath, required String title, required VoidCallback onTap}) {
    final baseColor = context.theme.bottomNavigationBarTheme.backgroundColor!;
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: 51.gw,
        height: 56.gw,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            /// 黑色背景
            Positioned(
              bottom: 0,
              child: Container(
                width: 51.gw,
                height: 44.gw,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.gw),
                  gradient: RadialGradient(
                    center: const Alignment(
                      (47.06 / 50 - 1), // Flutter center 从 -1~1
                      (0 / 50 - 1),
                    ),
                    radius: 0.7431, // 约 74.31% 半径
                    colors: [
                      baseColor.withOpacity(0),
                      baseColor.withOpacity(0.3),
                      baseColor.withOpacity(0.75),
                      baseColor,
                    ],
                    stops: const [
                      0.5049,
                      0.7084,
                      0.8638,
                      1.0,
                    ],
                  ),
                ),
              ),
            ),

            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          baseColor,
                          Colors.transparent,
                        ],
                        stops: const [0.6, 1.0], // 调整渐变起止位置
                      ).createShader(bounds);
                    },
                    blendMode: BlendMode.dstIn,
                    child: Image.asset(imagePath, height: 39.gw)),
                AneText(
                  title,
                  style: context.textTheme.title.fs10,
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
