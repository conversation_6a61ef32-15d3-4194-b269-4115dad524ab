import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/filter_video_list_entity.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';


FilterVideoListEntity $FilterVideoListEntityFromJson(Map<String, dynamic> json) {
  final FilterVideoListEntity filterVideoListEntity = FilterVideoListEntity();
  final List<VideoHotMovies>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<VideoHotMovies>(e) as VideoHotMovies).toList();
  if (records != null) {
    filterVideoListEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    filterVideoListEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    filterVideoListEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    filterVideoListEntity.current = current;
  }
  return filterVideoListEntity;
}

Map<String, dynamic> $FilterVideoListEntityToJson(FilterVideoListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  return data;
}

extension FilterVideoListEntityExtension on FilterVideoListEntity {
  FilterVideoListEntity copyWith({
    List<VideoHotMovies>? records,
    int? total,
    int? size,
    int? current,
  }) {
    return FilterVideoListEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current;
  }
}