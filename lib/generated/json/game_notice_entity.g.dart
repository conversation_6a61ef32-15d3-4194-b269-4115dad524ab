import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/game_notice_entity.dart';

GameNoticeListEntity $GameNoticeListEntityFromJson(Map<String, dynamic> json) {
  final GameNoticeListEntity gameNoticeListEntity = GameNoticeListEntity();
  final List<GameNoticeEntity>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GameNoticeEntity>(e) as GameNoticeEntity).toList();
  if (list != null) {
    gameNoticeListEntity.list = list;
  }
  return gameNoticeListEntity;
}

Map<String, dynamic> $GameNoticeListEntityToJson(GameNoticeListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension GameNoticeListEntityExtension on GameNoticeListEntity {
  GameNoticeListEntity copyWith({
    List<GameNoticeEntity>? list,
  }) {
    return GameNoticeListEntity()
      ..list = list ?? this.list;
  }
}

GameNoticeEntity $GameNoticeEntityFromJson(Map<String, dynamic> json) {
  final GameNoticeEntity gameNoticeEntity = GameNoticeEntity();
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    gameNoticeEntity.noticeTitle = noticeTitle;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    gameNoticeEntity.intoHistory = intoHistory;
  }
  return gameNoticeEntity;
}

Map<String, dynamic> $GameNoticeEntityToJson(GameNoticeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['noticeTitle'] = entity.noticeTitle;
  data['intoHistory'] = entity.intoHistory;
  return data;
}

extension GameNoticeEntityExtension on GameNoticeEntity {
  GameNoticeEntity copyWith({
    String? noticeTitle,
    int? intoHistory,
  }) {
    return GameNoticeEntity()
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..intoHistory = intoHistory ?? this.intoHistory;
  }
}